{"name": "dashboard", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"pinia": "^2.1.7", "vite-plugin-singlefile": "^0.13.5", "vooks": "^0.2.12", "vue": "^3.3.11", "vue-clipboard3": "^2.0.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.6", "@types/node": "^18.19.3", "@vitejs/plugin-vue": "^4.5.2", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.0", "jsdom": "^23.0.1", "naive-ui": "^2.36.0", "npm-run-all2": "^6.1.1", "typescript": "~5.3.0", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "0.25.1", "vite": "^5.0.10", "vitest": "^1.0.4", "vue-tsc": "^1.8.25"}}