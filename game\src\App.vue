<template>
  <n-config-provider :theme="theme as BuiltInGlobalTheme">
    <n-message-provider>
      <message-api></message-api>
    </n-message-provider>
    <n-dialog-provider>
      <dialog-api></dialog-api>
    </n-dialog-provider>
    <div ref="buttons" id="btn-container" v-if="game.inject" @wheel="onScrollReq">
      <function v-for="(fun, idx) in game.funcs" :data="fun" :index="idx" class="function" size="small">
        {{ fun.name }}
      </function>
    </div>
    <div v-else style="width: 100%;text-align: center;">
      <n-text id="tip" class="gradientText"> 正在加载功能列表，请稍等。。。</n-text>
    </div>

    <!-- 游戏iframe -->
    <iframe
      ref="gameFrame"
      src="./game-only.html"
      frameborder="0"
      class="game-iframe">
    </iframe>

    <n-global-style />
    <div id="patch"></div>
  </n-config-provider>
</template>
<script setup lang="ts">
import DialogApi from "@/components/dialog-api.vue";
import MessageApi from "@/components/message-api.vue";
import { darkTheme, useOsTheme } from 'naive-ui';
import type { BuiltInGlobalTheme } from "naive-ui/es/themes/interface";
import { useGameStore } from "@/stores/game";
import Function from "@/components/function.vue";
import ButtonGroup from "@/components/button-group/index.vue";
import Handler from "@/core/Handler";
import Evt from "@/core/evt";
import useClipboard from 'vue-clipboard3';
import type { VNode } from "vue";
import { render } from "vue";

const game = useGameStore();
const osTheme = useOsTheme();

const { toClipboard } = useClipboard();

const theme = ref<BuiltInGlobalTheme | undefined>(darkTheme);
Evt.on("THEME_CHANGE", Handler.alloc(null, (themeType: string) => {
  const isDark = themeType === 'dark';
  theme.value = isDark ? darkTheme : undefined;
}), false)
const themeAuto = ref(true)
// 监听主题变化
watch(
  osTheme,
  newValue => {
    if (!themeAuto.value) return
    Evt.emit("THEME_CHANGE", newValue);
  },
  { immediate: true }
);

const waitId = setInterval(() => {
  if (typeof window.DASHBOARD_EXEC != 'undefined') {
    // 获取初始化的主题颜色
    themeAuto.value = !!window.DASHBOARD_EXEC("GetThemeAuto()")
    const type = !!window.DASHBOARD_EXEC("GetThemeType()")
    Evt.emit("THEME_CHANGE", type ? 'light' : 'dark');
    clearInterval(waitId)
  }
}, 100)

// 设置游戏通信桥梁
const setupGameBridge = () => {
  window.GameBridge = {
    gameWindow: null,

    setGameWindow(win: any) {
      this.gameWindow = win;
      console.log('游戏窗口已连接');
    },

    callGame(method: string, ...args: any[]) {
      if (this.gameWindow && this.gameWindow[method]) {
        return this.gameWindow[method](...args);
      }
    }
  };
};

onMounted(() => {
  isMacOS.value = navigator.userAgent.indexOf("Macintosh") != -1;

  // 初始化游戏通信桥梁
  setupGameBridge();
  window.copy = (str: string, el?: any) => {
    const copyText = async (text) => {
      try {
        if (el)
          await toClipboard(text, el);
        else
          await toClipboard(text);
        window.$message.success("复制成功!")
      } catch (e) {
        console.error(e);
      }
    }
    copyText(str)
  }
  window.base64Encode = function (str: string) {
    let utf8Array = new TextEncoder().encode(str);
    let binaryStr = '';
    let len = utf8Array.byteLength;
    for (let i = 0; i < len; i++) {
      binaryStr += String.fromCharCode(utf8Array[i]);
    }
    return btoa(binaryStr);
  }
  window.base64Decode = function (str: string) {
    let base64DecodedBinaryStr = atob(str);
    let utf8Decoder = new TextDecoder('utf-8');
    return utf8Decoder.decode(new Uint8Array([...base64DecodedBinaryStr].map(char => char.charCodeAt(0))));
  }
  window.Events = window.Events || {};
  // evt事件
  window.Events["onMsg"] = game.onMsg;
  // http请求
  window.request = async (url: string) => {
    return new Promise((resolve, reject) => {
      fetch(url).then(async (data) => {
        if (data.status !== 200) {
          reject(data.statusText);
          return void window.$message.error("资源下载失败!请重启后重试", { duration: 10000 })
        }
        // uint8 转 base64传递
        const read = await data.body["getReader"]().read();
        return btoa(String.fromCharCode.apply(null, read.value));
      }).then(ret => {
        resolve(ret);
      });
    })
  }
  scrollAnimation()
  // 方法代码
  const function_listener = setInterval(async () => {
    if (window.REQ_JAVA_LOAD_FUNCTION != null) {
      let data: any = await window.REQ_JAVA_LOAD_FUNCTION();
      data = JSON.parse(data)
      for (const _ of data) {
        if (_.visible) {
          if (!eval(_.visible)) continue;
        }
        game.funcs.push(_)
      }
      clearInterval(function_listener)
    }
  }, 100)

  window.showTip = (vn: VNode) => {
    const el = document.getElementById("patch")
    if (el) {
      el.style.display = "inline-block"
      render(vn, el)
    }
  }
  window.hideTip = () => {
    const el = document.getElementById("patch")
    if (el) {
      el.style.display = "none"
    }
  }
});

const scrollAnimation = () => {
  setInterval(() => {
    if (buttons.value == null) return
    if (animCnt.value > 20) {
      return
    }
    buttons.value.scrollLeft += left.value ? -10 : 10;
    animCnt.value += 1;
  }, 16)
}
const isMacOS = ref(false);
const left = ref(false)
const animCnt = ref(21)
const buttons = ref<HTMLElement>(null)
const onScrollReq = (event) => {
  if (!isMacOS.value) {
    event.preventDefault();
    //buttons.value.scrollLeft += event.deltaY;
    left.value = event.deltaY < 0
    animCnt.value = 0
  }
}

//重载页面
const EVT_RELOAD = Handler.alloc(null, (evl: string) => {
  const dlg = window.$dialog.info({
    title: "刷新页面？",
    closable: false,
    autoFocus: false,
    content: () => "",
    action: () => h(ButtonGroup, {
      action: [
        {
          label: "确定", type: "success", callback: () => {
            dlg.destroy()
            eval(evl)
          }
        },
        { label: "取消", type: "default", callback: () => dlg.destroy() },
      ]
    })
  })
})
const EVT_BACK_TO_CITY = Handler.alloc(null, () => {
  if (window._world && !window._world.isEnterGame()) {
    return void window.$message.error("请先进入游戏")
  }
  eval("City.doEnterCity(xself.getId());")
})
// 事件列表
Evt.on("EVT_RELOAD", EVT_RELOAD, true)
// 我的城市
Evt.on("EVT_BACK_TO_CITY", EVT_BACK_TO_CITY, true)

const zoom = ref(1)

watchEffect(() => {
  // let v = 1 / zoom.value
  //
  // document.documentElement.style.setProperty('--game-height', `calc(100vh - ${(v * 36) + 4 + "px"})`);
  //
  // document.documentElement.style.setProperty('--btn-container-height', v * 36 + "px");
  //
  // document.documentElement.style.setProperty('--function-btn-zoom', v);
  //
  // let dv = 1
  // if (v == 2) {
  //   dv = 1.5
  // }
  // if (v == 3) {
  //   dv = 2.2
  // }
  // if (v == 4) {
  //   dv = 3
  // }
  //
  // document.documentElement.style.setProperty('--dialog-scale', dv);

});

window["ON_ZOOM_EVT"] = (lv: number) => {
  if (zoom.value == lv) return
  zoom.value = lv
  window.X_SCALE = lv
  setTimeout(() => {
    try {
      //@ts-ignore
      Main.instance.stage.setContentSize(640, 960)
      //@ts-ignore
      nato.initialize(640, 960, true)
    } catch (e) {
      window.$message.error("缩放出错，请刷新页面！")
    }
  }, 1000)
}

</script>

<style>
* {
  user-select: none
}

#app {
  width: 100vw;
  height: 40px;
  overflow: hidden;
}

/* 变窄 */
::-webkit-scrollbar {
  width: 1px;
  height: 2px;
  display: none;
}

/* 渐变背景 */
::-webkit-scrollbar-track {
  background: white;
}

/* 圆角 */
::-webkit-scrollbar-thumb {
  border-radius: 1px;
  background-color: #0452d9;
}

#tip {
  line-height: 48px;
  font-weight: bolder;
  transition: color .5s;
}

/* 游戏iframe样式 */
.game-iframe {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  border: none;
  z-index: 999;
}

@keyframes gradient {
  0% {
    color: red;
  }

  /*起始状态*/
  50% {
    color: blue;
  }

  /*过渡状态*/
  100% {
    color: green;
  }

  /*结束状态*/
}

.gradientText {
  animation: gradient 3s infinite alternate;
  /*应用动画并设置持续时间、重复次数等属性*/
}

#btn-container {
  position: absolute;
  bottom: 2px;
  width: 100vw;
  height: var(--btn-container-height, 36px);
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
  background-color: rgba(17, 17, 17, 0.7);
  color: white !important;
  overflow-x: auto;
  overflow-y: hidden;
}

#btn-container:first-child {
  padding-left: 10px;
  padding-right: 10px;
}

.function {
  color: white !important;
}

.btn-cbs {
  background: linear-gradient(90deg, #8bdeda, #43add0, #998ee0, #e17dc2, #ef9393, #8bdeda);
  background-size: 400%;
}

.btn-cbs:before {
  content: "";
  position: absolute;
  background: inherit;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 6px;
  filter: blur(20px);
  opacity: 0;
  transition: opacity .5s;
  z-index: -1;
}

.btn-cbs {
  z-index: 1;
  animation: glow 8s linear infinite;
}

@keyframes glow {
  0% {
    background-position: 0;
  }

  100% {
    background-position: 400%;
  }
}
</style>
