[1, ["42YlG8C+VFQIQhi/uTnF/K", "d5xxX9FgdEXrimuuZccNUd", "1cKxmGiHhCGJmX4JbbRuk+", "13iPuq9JBMd63Sy5gdsHXO", "75Y95m7phJp52lzyY6XTsO", "3bJHtDRAVJX57xWjaUM1yv", "94FGSZedBCE46Kdw4aaHkw@f9941"], ["node", "asset", "targetInfo", "root", "value", "_parent", "_font", "guidePrefab", "_cameraComponent", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "BottomLayer", "topNode", "effectNode", "headNode", "itemNode", "thingNode", "mapEffectNode", "mapNode", "sceneCamera", "camera", "shortMsgParent", "scene"], [["cc.Node", ["_name", "_layer", "_id", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_components", "_parent", "_children", "_lpos", "_prefab"], -3, 9, 1, 2, 5, 4], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.Widget", ["_alignFlags", "_originalHeight", "_left", "_originalWidth", "_verticalCenter", "_right", "node"], -3, 1], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_lpos"], 1, 1, 2, 5], ["cc.Camera", ["_projection", "_priority", "_orthoHeight", "_near", "_far", "_visibility", "_clearFlags", "_color", "node"], -4, 5, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.SceneAsset", ["_name"], 2], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["9da1bhnFdVCw4E9gDcwGIw+", ["node", "camera", "sceneCamera", "mapNode", "mapEffectNode", "thingNode", "itemNode", "headNode", "effectNode", "topNode"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -2, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "node"], 0, 1], ["893a4dR4xFCT6g9HLSe2GND", ["node", "shortMsgParent"], 3, 1, 1], ["cc.Label", ["_string", "_horizontalAlign", "_actualFontSize", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_outlineWidth", "node", "_color", "_font"], -6, 1, 5, 6], ["96e1ftejFNLpLRv7xkHWTM+", ["node"], 3, 1], ["cc.UIOpacity", ["node"], 3, 1], ["e0e78oy1CZBsq48297xT3dw", ["node", "attNode"], 3, 1, 1], ["a1b75yR+R5Lg7PQp7ZdqnlO", ["node", "BottomLayer", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guidePrefab"], 3, 1, 1, 1, 1, 1, 6], ["f9354FNBl5AI7d3pWxHQXuL", ["node", "headNode", "thingItemNode"], 3, 1, 1, 1], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["c4b53unrtZM6KKiF2GPPHU9", ["node", "loading"], 3, 1, 1], ["682c6os+ZRPUIK9PZbIsUT6", ["node"], 3, 1], ["cc.AudioSource", ["_loop", "_playOnAwake", "_volume", "node"], 0, 1], ["a2cf0sX3ExAXp//tYZLraCi", ["node"], 3, 1], ["4887aAgI5dAz4fQ/dcg71Zi", ["node"], 3, 1], ["47b45LdZEVKt79Hg7tv8w3h", ["node"], 3, 1], ["ca57dOVYLJK0bIpcjTdX3wz", ["node"], 3, 1], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", [], 3], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3]], [[17, 0, 2], [11, 0, 1, 2, 3], [14, 0, 1, 2, 3], [1, 0, 1, 1], [13, 0, 1, 2, 2], [0, 0, 1, 7, 6, 3], [2, 0, 3, 1, 6, 4], [1, 0, 1], [12, 0, 1, 2, 2], [0, 3, 4, 7, 10, 3], [10, 0, 1, 2, 3], [0, 0, 7, 8, 6, 2], [0, 0, 7, 6, 2], [0, 0, 7, 6, 9, 2], [5, 0, 1, 2, 3, 4, 5, 4], [5, 0, 2, 1, 3, 4, 5, 4], [15, 0, 1, 2, 2], [22, 0, 1], [6, 0, 2], [7, 0, 1, 2, 3, 2], [0, 0, 2, 8, 6, 9, 3], [0, 0, 1, 7, 8, 6, 3], [0, 0, 7, 8, 6, 9, 2], [0, 0, 5, 6, 3], [3, 0, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 3], [4, 0, 1, 2, 3, 4, 6, 5, 7, 8], [4, 0, 1, 2, 3, 4, 5, 8, 7, 7], [1, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [9, 0, 1, 2, 3, 4, 5, 6], [16, 0, 1, 2, 2], [2, 0, 2, 4, 6, 4], [2, 0, 2, 5, 1, 6, 5], [18, 0, 1, 2, 3, 4], [19, 0, 1, 1], [20, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 10], [21, 0, 1], [23, 0, 1, 1], [24, 0, 1, 2, 3, 4, 5, 1], [25, 0, 1, 2, 1], [26, 0, 1, 1], [27, 0, 1, 1], [28, 0, 1], [29, 0, 1, 2, 3, 4], [30, 0, 1], [31, 0, 1], [32, 0, 1], [33, 0, 1], [34, 0, 1, 2, 3, 4, 5, 6, 7, 1], [35, 0, 1, 1], [36, 0, 1, 1], [37, 1], [38, 1], [39, 1], [40, 1], [41, 1], [42, 1]], [[18, "main"], [20, "<PERSON><PERSON>", "beI88Z2HpFELqR4T5EMHpg", [-13, -14, -15, -16, -17, -18, -19], [[3, -1, [5, 750, 1334]], [41, -3, -2], [33, 45, 5.684341886080802e-14, 5.684341886080802e-14, 750, -4], [42, -6, -5], [43, -7], [44, true, false, 0.5, -8], [45, -9], [46, -10], [47, -11], [48, -12]], [1, 375.00000000000006, 667, 0]], [11, "<PERSON><PERSON><PERSON><PERSON>", 1, [-28, -29, -30, -31, -32, -33], [[3, -20, [5, 750, 1334]], [6, 45, 100, 100, -21], [17, -22], [39, -27, -26, -25, -24, -23, 6]]], [21, "MainScene", 1, 1, [-45, -46, -47, -48, -49, -50, -51], [[3, -34, [5, 0, 0]], [29, -44, -43, -42, -41, -40, -39, -38, -37, -36, -35]]], [0, ["892DrGDvlNcrpvJc5RLAhC"]], [11, "<PERSON><PERSON><PERSON><PERSON>", 2, [-54, -55], [[3, -52, [5, 750, 1334]], [6, 45, 100, 100, -53]]], [23, "lbTemplate", false, [[28, -56, [5, 66.46, 16], [0, 0, 0.5]], [36, "默认文本", 0, 16, 16, 16, false, false, true, 1, -57, [4, 4278255360], 4], [37, -58], [17, -59]]], [0, ["80iqecRCBMNobyD2MEIGsn"]], [0, ["897C4gtBZAg57ab+4kOVYU"]], [11, "FloatingNode", 2, [-64], [[3, -60, [5, 750, 1334]], [35, -62, -61], [6, 45, 100, 100, -63]]], [0, ["c46/YsCPVOJYA4mWEpNYRx"]], [22, "MsgAtt", 5, [6], [[3, -65, [5, 10, 10]], [32, 10, 321.93399999999997, -159.284, -66], [38, -67, 6]], [1, -48.06600000000003, -159.284, 0]], [5, "ItemNode", 1, 3, [[7, -68]]], [5, "HeadNode", 1, 3, [[7, -69]]], [9, 0, {}, 1, [14, "60qE5GjjdM3Ia1X7Q17mAQ", null, null, -74, [10, "b950TzBV5Ik6yUsJ9tUqMC", null, [[1, "LoadingUI", ["_name"], [0, ["60qE5GjjdM3Ia1X7Q17mAQ"]]], [8, ["_lpos"], [0, ["60qE5GjjdM3Ia1X7Q17mAQ"]], [1, 0, 0, 0]], [8, ["_lrot"], [0, ["60qE5GjjdM3Ia1X7Q17mAQ"]], [3, 0, 0, 0, 1]], [4, ["_euler"], -70, [1, 0, 0, 0]], [2, false, ["_isSystemFontUsed"], 4], [8, ["_contentSize"], [0, ["8apaUh5xxLPLtfgnjn9fhE"]], [5, 99.85, 22]], [16, ["_font"], 4, 1], [2, 22, ["_fontSize"], 4], [2, 22, ["_actualFontSize"], 4], [2, 22, ["_lineHeight"], 4], [4, ["_color"], 4, [4, 4294967295]], [2, "loading...", ["_string"], 4], [2, false, ["_isSystemFontUsed"], -71], [8, ["_contentSize"], [0, ["699xGHWLlPgYNzLdLWYui0"]], [5, 0, 20]], [16, ["_font"], -72, 2], [2, 1073741824, ["_layer"], -73], [1, 1073741824, ["_layer"], [0, ["76zQwR8BhH/rpNh+YNk8qC"]]], [1, 1073741824, ["_layer"], [0, ["10TLnY5DpFrrSxr7h/s0Ui"]]], [1, 1073741824, ["_layer"], [0, ["5b3spkRspKXLVrHxrkihwk"]]], [1, 1073741824, ["_layer"], [0, ["74egNZUgBB2abk1J0bhU+5"]]], [1, 1073741824, ["_layer"], [0, ["2fRmUvyPBOsbbHXtt+mbqu"]]], [1, 1073741824, ["_layer"], [0, ["8dmED5aKNOW4lPgjoAM9Mc"]]]]], 0]], [12, "BottomLayer", 2, [[3, -75, [5, 750, 1334]], [6, 45, 100, 100, -76]]], [12, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 2, [[3, -77, [5, 750, 1334]], [6, 45, 100, 100, -78]]], [12, "Pop<PERSON>ayer", 2, [[3, -79, [5, 750, 1334]], [6, 45, 100, 100, -80]]], [13, "shortMsg", 9, [[3, -81, [5, 0, 1]], [34, 1, 1, true, -82]], [1, 0, -250.724, 0]], [26, 0, 1073741824, 667, 0, 2000, 0, 1108344832, [4, 4278190080]], [5, "MapNode", 1, 3, [[3, -83, [5, 0, 0]]]], [5, "MapEffectNode", 1, 3, [[3, -84, [5, 0, 0]]]], [5, "ThingNode", 1, 3, [[3, -85, [5, 0, 0]]]], [5, "EffectNode", 1, 3, [[7, -86]]], [5, "TopNode", 1, 3, [[7, -87]]], [9, 0, {}, 5, [14, "c46/YsCPVOJYA4mWEpNYRx", null, null, -88, [10, "00/ECC3S1HArMIbjIx3Oqh", null, [[2, "MsgBottomCenter", ["_name"], 10], [4, ["_lpos"], 10, [1, 0, -407, 0]], [4, ["_lrot"], 10, [3, 0, 0, 0, 1]], [4, ["_euler"], 10, [1, 0, 0, 0]], [8, ["_contentSize"], [0, ["03DL3lO39GLJLV6Woq0DSW"]], [5, 500.7998046875, 56.4]], [2, 1073741824, ["_layer"], 10], [1, 1073741824, ["_layer"], [0, ["48GBxXRiNJIbKRF+gGQilR"]]]]], 3]], [9, 0, {}, 2, [15, "80iqecRCBMNobyD2MEIGsn", null, [], -89, [10, "f4yKIg+b1P8Jw57YD028Nf", null, [[2, "ChatBullet", ["_name"], 7], [4, ["_lpos"], 7, [1, 0, 569.0679999999999, 0]], [4, ["_lrot"], 7, [3, 0, 0, 0, 1]], [4, ["_euler"], 7, [1, 0, 0, 0]], [2, true, ["_active"], 7], [2, 1073741824, ["_layer"], 7], [1, 1073741824, ["_layer"], [0, ["0c0wRc2ORIJ5KzI9SlZTtt"]]], [1, 1073741824, ["_layer"], [0, ["beZCCLUSJNeqwXd3nh5CBl"]]], [1, 1073741824, ["_layer"], [0, ["497kacGllP9JmhurggCSGS"]]], [1, 1073741824, ["_layer"], [0, ["29pgu1OZJJLooP6mbAvaaO"]]], [1, 1073741824, ["_layer"], [0, ["58IbLuzk5H+IWIpDAAf+kS"]]], [1, 1073741824, ["_layer"], [0, ["bb7ZkL5vlA4JUEngQfCX6u"]]], [1, 1073741824, ["_layer"], [0, ["a0iOyZM7BAoZzU1HKEDxtV"]]], [1, 1073741824, ["_layer"], [0, ["a6zhOrenJDeI7Za6PtrXuH"]]], [1, 1073741824, ["_layer"], [0, ["56WiCSq3xI1LmfqsWRJ/u/"]]], [1, 1073741824, ["_layer"], [0, ["93CLjW0vRP7aHXzEbx0KoZ"]]], [1, 1073741824, ["_layer"], [0, ["8ek8OeQSBN9q1A/dRMGU3S"]]], [1, 1073741824, ["_layer"], [0, ["744Wg6R3tGu4Rn5eh1ZQz+"]]], [1, 1073741824, ["_layer"], [0, ["8aDeAiYEtEAav2LoMNJ/Xk"]]], [1, 1073741824, ["_layer"], [0, ["4f5+MAhOpP/IGSBZHw/aYK"]]]]], 5]], [13, "PoolNode", 1, [[7, -90], [40, -91, 13, 12]], [1, -20000, 0, 0]], [9, 0, {}, 1, [15, "897C4gtBZAg57ab+4kOVYU", null, [], -92, [10, "a4VJKJyrtN7ZdrIRscXEuM", null, [[2, "TopPopup", ["_name"], 8], [4, ["_lpos"], 8, [1, 0, 0, 0]], [4, ["_lrot"], 8, [3, 0, 0, 0, 1]], [4, ["_euler"], 8, [1, 0, 0, 0]], [2, false, ["_active"], 8], [2, 1073741824, ["_layer"], 8], [31, ["_spriteFrame"], [0, ["d04WpdmbhKvpy7oxpoVte2"]], 8]]], 7]], [19, "main", [1], [30, null, null, "956a77a0-b634-4992-81bc-ab628e898115", null, [], [14, 25, 26, 28]], [49, [50, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [51, [4, 4283190348], [0, 512, 512]], [52], [53], [54], [55], [56], [57]]], [24, "Camera", 1, [19], [1, 0, 0, 1000]], [25, "SceneCamera", 1, 1, [-93], [1, 0, 0, 1000]], [27, 0, 1, 667, 0, 2000, 7, 31, [4, 4278190080]], [0, ["60qE5GjjdM3Ia1X7Q17mAQ"]], [0, ["68DzlC2JFOAK+XE9n+CxPt"]]], 0, [0, 0, 1, 0, 8, 19, 0, 0, 1, 0, 0, 1, 0, 9, 14, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 30, 0, -2, 31, 0, -3, 3, 0, -4, 14, 0, -5, 2, 0, -6, 27, 0, -7, 28, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 10, 5, 0, 11, 17, 0, 12, 16, 0, 13, 15, 0, 0, 2, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -4, 9, 0, -5, 5, 0, -6, 26, 0, 0, 3, 0, 14, 24, 0, 15, 23, 0, 16, 13, 0, 17, 12, 0, 18, 22, 0, 19, 21, 0, 20, 20, 0, 21, 32, 0, 22, 19, 0, 0, 3, 0, -1, 20, 0, -2, 21, 0, -3, 12, 0, -4, 22, 0, -5, 13, 0, -6, 23, 0, -7, 24, 0, 0, 5, 0, 0, 5, 0, -1, 25, 0, -2, 11, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 9, 0, 23, 18, 0, 0, 9, 0, 0, 9, 0, -1, 18, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 2, 33, 0, 2, 34, 0, 2, 34, 0, 2, 33, 0, 3, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 3, 25, 0, 3, 26, 0, 0, 27, 0, 0, 27, 0, 3, 28, 0, -1, 32, 0, 24, 29, 1, 5, 29, 6, 5, 11, 19, 0, 30, 93], [0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 4, 4, 1, 6, 1, 7, 1, 4], [1, 0, 0, 2, 0, 3, 4, 5, 6]]