var _world = globalThis;
var map = _world.MapManager;
_world.wait = async function (delay) {
    return new Promise(resolve => setTimeout(() => {
        resolve(0)
    }
        , delay))
};

_world.fb = [
    // { map: "S_f1001", x: 104, y: 17, nextTime: 0 }, // 欲火裂隙
    { map: "S_f1018", x: 43, y: 49, nextTime: 0 }, // 欲火裂隙
    // { map: "S_f1002", x: 67, y: 49, nextTime: 0 }, // 蛇沼虫窟
    // { map: "S_f1003", x: 26, y: 46, nextTime: 0 }, // 无光矿坑
    // { map: "S_f1004", x: 23, y: 64, nextTime: 0 }, // 黑月城堡
];

_world.arg = {
    run: true,
};
_world.eachFb = async (mapId, x, y) => {
    MapManager.fly2Pos(mapId, x, y)
    while (true) {
        await _world.wait(1000);
        if (Main.playerData.curMapId != mapId) {
            continue
        }
        break
    }
    // 初始化怪物
    var bossInfo = ConfigManager.getAllData("TuiJianGuaJiData").filter(m => m.zuobiao[0] == Main.playerData.curMapId).map(m => {
        return {
            belongTo: "",
            canRelive: 0,
            curHp: "25767",
            death: 0,
            dir: 2,
            group: "3",
            id: "",
            lv: 16,
            maxHp: "25767",
            monsterName: "怪物",
            moveSpeed: 30,
            reliveTime: 0,
            tId: m.monsterID,
            x: Number(m.zuobiao[1]),
            y: Number(m.zuobiao[2])
        }
    })
    if (bossInfo.length == 0) {
        bossInfo = [
            {
                belongTo: "",
                canRelive: 0,
                curHp: "1774948",
                death: 0,
                dir: 2,
                group: "3",
                id: "",
                lv: 18,
                maxHp: "1774948",
                monsterName: "暗熔酒吧老板",
                moveSpeed: 30,
                reliveTime: 0,
                tId: "S_Bs2019005",
                x: 21,
                y: 18
            }
        ]
    }
    // 发送消息
    var send = (ary) => eventCenter.instance.emit(321, 0, {
        allocationType: 0,
        leaderId: Main.playerData.id,
        leftime: "1799538",
        teamMode: 1,
        bossInfo: ary
    })
    send(bossInfo)
    await _world.wait(2000);
    // 依次杀怪
    var handleNext = async () => {
        var data = bossInfo[0]
        if (!data) {
            var fbMapId = Main.playerData.curMapId;
            Net.sendMsg(214, { str: Main.playerData.curMapId })
            while (true) {
                await _world.wait(1000);
                if (Main.playerData.curMapId == fbMapId) {
                    continue
                }
                break
            }
            await _world.wait(2000);
            return
        }

        PlayerControl.startAutoMoveToMap(Main.playerData.curMapId, data.x, data.y, 1, { cmd: "autofight" })
        while (true) {
            await _world.wait(1000);
            let stop = false;
            for (var obj of ThingManager.instance.mThingDataMap) {
                if (!obj || obj.length != 2) continue
                var [key, val] = obj
                if (val.getString(5005) != data.tId) continue
                if (val.getValue(3018)) {
                    stop = true
                    break
                }
            }
            if (stop) break
            if (!Main.playerData.isAutoFighting) {
                PlayerControl.startAutoMoveToMap(Main.playerData.curMapId, data.x, data.y, 1, { cmd: "autofight" })
            }
        }
        bossInfo.shift()
        await handleNext()
    }
    await handleNext()
}


_world.each = async function () {
    while (_world.arg.run) {
        // 找到最早可进入的副本
        let readyDungeon = _world.fb
            .filter(d => Date.now() >= d.nextTime)
            .sort((a, b) => a.nextTime - b.nextTime)[0];
        if (readyDungeon) {
            // 进入副本
            await this.eachFb(readyDungeon.map, readyDungeon.x, readyDungeon.y)
            // 设置下次可进入时间（1分钟后）
            readyDungeon.nextTime = Date.now() + 60 * 1000;
            await _world.wait(2000);
        } else {
            // 等待最早的副本刷新
            let nextReady = Math.min(..._world.fb.map(d => d.nextTime));
            let waitTime = nextReady - Date.now();
            await _world.wait(Math.max(waitTime, 1000));
        }
        const items = []
        ItemManager.getInstance().itemsInfo[4].forEach(i => {
            if (!i) return
            const cfg = ItemManager.getCfgById(i.tempId)
            if (!cfg) return
            if (cfg.quality >= 4) return
            if (i.itemLv > 70 && i.forgeAttr?.length) {
                if (i.forgeAttr.filter(f => f.quality == 5).length >= 3) return
            }
            i.uuid && items.push(i.uuid)
        })
        if (items.length > 5) {
            Net.sendPbMsg(59, { items: items })
            await _world.wait(1000);
        }
    }
}