{"libVersion": "2.27.3", "setting": {"urlCheck": false, "bigPackageSizeSupport": true, "coverView": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": false, "useApiHostProcess": false, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "useIsolateContext": false}, "projectname": "yxmxt", "condition": {}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html"}