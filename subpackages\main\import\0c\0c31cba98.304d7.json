[1, ["faQO1SSTFOvo4Bd/0qdjpc@e65fc", "faQO1SSTFOvo4Bd/0qdjpc@8c199", "94FGSZedBCE46Kdw4aaHkw@f9941", "faQO1SSTFOvo4Bd/0qdjpc@6b2bf", "faQO1SSTFOvo4Bd/0qdjpc@a77c2", "faQO1SSTFOvo4Bd/0qdjpc@993b1", "faQO1SSTFOvo4Bd/0qdjpc", "50l5UEQFFHCYtSvnEVn/mW", "ea2PqyuYFI/otYXdxLbcdg@f9941", "1epxEQnvZNWJZRplACIdXZ@6c48a", "5cg8fpN69H7LH24b4gsGei@6c48a", "63snwFAZZEm4q5MM6k/cur@6c48a", "faQO1SSTFOvo4Bd/0qdjpc@acae1", "faQO1SSTFOvo4Bd/0qdjpc@8da69", "faQO1SSTFOvo4Bd/0qdjpc@8026d", "faQO1SSTFOvo4Bd/0qdjpc@40510", "faQO1SSTFOvo4Bd/0qdjpc@dd058", "faQO1SSTFOvo4Bd/0qdjpc@760ca", "faQO1SSTFOvo4Bd/0qdjpc@f93a6", "faQO1SSTFOvo4Bd/0qdjpc@d95ee", "faQO1SSTFOvo4Bd/0qdjpc@25adb", "faQO1SSTFOvo4Bd/0qdjpc@cb6a6", "faQO1SSTFOvo4Bd/0qdjpc@e6287", "faQO1SSTFOvo4Bd/0qdjpc@c0180", "faQO1SSTFOvo4Bd/0qdjpc@c5574", "faQO1SSTFOvo4Bd/0qdjpc@fc8d6", "faQO1SSTFOvo4Bd/0qdjpc@5a989", "faQO1SSTFOvo4Bd/0qdjpc@67f4a", "faQO1SSTFOvo4Bd/0qdjpc@de01f", "faQO1SSTFOvo4Bd/0qdjpc@3eb79", "99zsborI9EQpKxhoIDEO4Q@f9941", "1epxEQnvZNWJZRplACIdXZ@f9941", "5cg8fpN69H7LH24b4gsGei@f9941", "63snwFAZZEm4q5MM6k/cur@f9941", "30A25BxZNL4IXP7nGnXiyp@f9941", "faQO1SSTFOvo4Bd/0qdjpc@5ce1b", "faQO1SSTFOvo4Bd/0qdjpc@24c53", "ea2PqyuYFI/otYXdxLbcdg@6c48a"], ["node", "_spriteFrame", "_target", "_parent", "_textureSource", "_atlas", "_cameraComponent", "tishiNode", "lab2", "btnLogin", "btnEnter", "ipcLab", "logo", "btnNotice", "bg", "yinsiNode", "errorTipsNode", "alertYear", "loadingNode", "noticeRich", "svrList2", "svrList1", "txtBanHao", "agree<PERSON><PERSON><PERSON>", "statement", "gameVer", "statementNode", "lastSvrNode", "alertNode", "noticeNode", "selectedServerNode", "_checkMark", "_content", "scene", "_defaultClip"], [["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_fontFamily", "_lineHeight", "_enableOutline", "_enableWrapText", "_isBold", "_cacheMode", "_outlineWidth", "_overflow", "_horizontalAlign", "node", "_color"], -9, 1, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_spriteFrame", "_color", "_atlas"], -1, 1, 6, 5, 6], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_alignMode", "_left", "_right", "_originalWidth", "_originalHeight", "_enabled", "_horizontalCenter", "node"], -7, 1], ["cc.Node", ["_name", "_active", "_id", "_components", "_parent", "_children", "_lpos", "_lrot", "_euler"], 0, 9, 1, 2, 5, 5, 5], ["cc.Layout", ["_layoutType", "_resizeMode", "_spacingY", "_isAlign", "_paddingTop", "_spacingX", "_affectedByScale", "_paddingLeft", "node"], -5, 1], ["cc.Node", ["_name", "_active", "_parent", "_components", "_lpos", "_children"], 1, 1, 12, 5, 2], "cc.SpriteFrame", ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "clickEvents", "_normalColor", "_target"], 2, 1, 9, 5, 1], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_fontFamily", "_verticalAlign", "_maxWidth", "_cacheMode", "node"], -4, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Toggle", ["_isChecked", "node", "_normalColor", "_target", "_checkMark", "checkEvents"], 2, 1, 5, 1, 1, 9], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["682c6os+ZRPUIK9PZbIsUT6", ["node"], 3, 1], ["37134kXueZKwLhkNPLRTV0E", ["node", "selectedServerNode", "noticeNode", "alertNode", "lastSvrNode", "statementNode", "gameVer", "statement", "agree<PERSON><PERSON><PERSON>", "txtBanHao", "svrList1", "svrList2", "noticeRich", "loadingNode", "alertYear", "errorTipsNode", "yinsiNode", "bg", "btnNotice", "logo", "logoFrames", "ipcLab", "btnEnter", "btnLogin", "lab2", "tishiNode"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1], ["cc.ToggleContainer", ["node", "checkEvents"], 3, 1, 9], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "_content"], 0, 1, 1], ["cc.Mask", ["node"], 3, 1], ["cc.Graphics", ["node", "_fillColor"], 3, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["96e1ftejFNLpLRv7xkHWTM+", ["node"], 3, 1], ["ef5ecZ//yBDdZrR2hWnJlKA", ["node"], 3, 1], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.WebView", ["_url", "node"], 2, 1], ["cc.Scene", ["_name", "autoReleaseAssets", "_children", "_globals"], 1, 2, 4], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", [], 3], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.Camera", ["_projection", "_priority", "_orthoHeight", "_near", "_far", "_visibility", "node", "_color"], -3, 1, 5], ["96925NpxVhGX5GHR06EhbFH", ["node", "tmpNode", "pageChangeEvent"], 3, 1, 1, 4]], [[7, 0, 1, 1], [3, 0, 4, 3, 6, 2], [22, 0, 1], [7, 0, 1, 2, 1], [10, 0, 1, 2, 3], [3, 0, 4, 5, 3, 6, 2], [3, 0, 4, 3, 2], [8, 0, 1, 2, 3, 4, 2], [1, 0, 1, 4, 5, 3], [21, 0, 1], [2, 0, 6, 7, 10, 4], [3, 0, 1, 4, 5, 3, 3], [3, 0, 4, 5, 3, 2], [2, 0, 2, 10, 3], [1, 0, 4, 5, 2], [1, 2, 0, 4, 5, 3], [0, 0, 1, 2, 3, 4, 6, 7, 8, 5, 12, 10], [5, 0, 2, 3, 4, 2], [1, 1, 4, 5, 2], [3, 0, 1, 4, 3, 6, 3], [2, 0, 6, 3, 10, 4], [2, 0, 6, 7, 3, 10, 5], [1, 4, 5, 1], [8, 1, 2, 1], [18, 0, 1, 2, 3, 4, 4], [19, 0, 1], [20, 0, 1, 1], [0, 0, 1, 2, 3, 4, 6, 7, 8, 5, 12, 13, 10], [3, 0, 5, 3, 2], [5, 0, 2, 5, 3, 4, 2], [5, 0, 2, 3, 2], [1, 0, 1, 4, 6, 5, 3], [8, 0, 1, 2, 2], [9, 0, 1, 4, 2, 3, 7, 6], [3, 0, 5, 3, 6, 2], [3, 0, 1, 4, 5, 3, 6, 3], [5, 0, 1, 2, 3, 4, 3], [1, 1, 4, 2], [1, 2, 0, 1, 4, 5, 4], [1, 0, 1, 4, 3], [10, 1], [4, 1, 0, 5, 8, 4], [23, 0, 1], [0, 0, 1, 2, 3, 4, 6, 7, 8, 5, 9, 12, 11], [0, 0, 1, 2, 3, 4, 6, 7, 5, 12, 9], [0, 0, 1, 2, 3, 4, 6, 7, 8, 5, 9, 12, 13, 11], [37, 0, 1, 2, 1], [12, 0, 2], [3, 0, 2, 5, 3, 6, 3], [3, 0, 4, 3, 6, 7, 8, 2], [3, 0, 1, 4, 3, 3], [5, 0, 1, 2, 3, 3], [13, 0, 1, 2, 3, 2], [7, 0, 1], [7, 0, 2, 1], [14, 0, 1, 1], [2, 0, 4, 5, 1, 2, 10, 6], [2, 8, 0, 2, 10, 4], [2, 0, 4, 1, 10, 4], [2, 0, 4, 10, 3], [2, 0, 5, 1, 10, 4], [2, 0, 2, 3, 10, 4], [2, 0, 5, 1, 2, 10, 5], [2, 0, 1, 3, 10, 4], [2, 0, 10, 2], [2, 0, 9, 10, 3], [15, 0, 1], [16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 1], [1, 3, 2, 0, 4, 4], [1, 1, 4, 6, 5, 2], [1, 0, 4, 5, 7, 2], [1, 4, 5, 7, 1], [1, 4, 1], [1, 2, 0, 1, 4, 4], [8, 1, 1], [11, 1, 2, 3, 4, 1], [11, 0, 1, 2, 3, 5, 4, 2], [4, 1, 0, 4, 2, 3, 8, 6], [4, 0, 2, 8, 3], [4, 1, 0, 8, 3], [4, 1, 0, 7, 4, 5, 2, 3, 8, 8], [4, 1, 0, 6, 8, 4], [4, 1, 0, 6, 3, 8, 5], [17, 0, 1, 1], [9, 0, 1, 2, 5, 3, 6, 7, 7], [9, 0, 1, 4, 2, 5, 3, 7, 7], [0, 0, 1, 2, 3, 4, 6, 8, 9, 12, 9], [0, 0, 1, 2, 3, 4, 10, 6, 7, 5, 9, 12, 13, 11], [0, 0, 1, 2, 3, 4, 10, 6, 7, 5, 12, 10], [0, 0, 11, 1, 2, 3, 4, 10, 7, 5, 12, 10], [0, 0, 1, 2, 3, 4, 6, 7, 5, 9, 12, 10], [0, 0, 1, 2, 3, 6, 7, 5, 9, 12, 9], [0, 0, 1, 2, 3, 4, 6, 7, 5, 12, 13, 9], [0, 0, 1, 2, 3, 4, 6, 5, 12, 13, 8], [0, 0, 1, 2, 3, 4, 10, 7, 8, 5, 12, 10], [0, 0, 1, 2, 3, 4, 7, 8, 5, 12, 9], [0, 0, 1, 2, 3, 4, 6, 5, 12, 8], [24, 0, 1, 2, 3, 2], [25, 0, 1, 2], [26, 0, 1, 2, 3, 3], [27, 0, 1, 2, 3, 4, 5, 6, 7, 1], [28, 0, 1, 1], [29, 0, 1, 1], [30, 1], [31, 1], [32, 1], [33, 1], [34, 1], [35, 1], [36, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "logo_doumo", "rect": {"x": 50, "y": 122, "width": 645, "height": 218}, "offset": {"x": -2.5, "y": -28}, "originalSize": {"width": 750, "height": 406}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-322.5, -109, 0, 322.5, -109, 0, -322.5, 109, 0, 322.5, 109, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [50, 284, 695, 284, 50, 66, 695, 66], "nuv": [0.06666666666666667, 0.1625615763546798, 0.9266666666666666, 0.1625615763546798, 0.06666666666666667, 0.6995073891625616, 0.9266666666666666, 0.6995073891625616], "minPos": {"x": -322.5, "y": -109, "z": 0}, "maxPos": {"x": 322.5, "y": 109, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [4], [9]], [[{"name": "logo_spl", "rect": {"x": 79, "y": 83, "width": 595, "height": 316}, "offset": {"x": 1.5, "y": 0}, "originalSize": {"width": 750, "height": 482}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-297.5, -158, 0, 297.5, -158, 0, -297.5, 158, 0, 297.5, 158, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [79, 399, 674, 399, 79, 83, 674, 83], "nuv": [0.10533333333333333, 0.17219917012448133, 0.8986666666666666, 0.17219917012448133, 0.10533333333333333, 0.8278008298755186, 0.8986666666666666, 0.8278008298755186], "minPos": {"x": -297.5, "y": -158, "z": 0}, "maxPos": {"x": 297.5, "y": 158, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [4], [10]], [[{"name": "logo_yzmj", "rect": {"x": 103, "y": 83, "width": 544, "height": 316}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 750, "height": 482}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-272, -158, 0, 272, -158, 0, -272, 158, 0, 272, 158, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [103, 399, 647, 399, 103, 83, 647, 83], "nuv": [0.13733333333333334, 0.17219917012448133, 0.8626666666666667, 0.17219917012448133, 0.13733333333333334, 0.8278008298755186, 0.8626666666666667, 0.8278008298755186], "minPos": {"x": -272, "y": -158, "z": 0}, "maxPos": {"x": 272, "y": 158, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [4], [11]], [[[47, "zoneServer"], [48, "<PERSON><PERSON>", "beI88Z2HpFELqR4T5EMHpg", [-31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52], [[0, -1, [5, 750, 1334]], [55, -3, -2], [56, 45, 5.684341886080802e-14, 5.684341886080802e-14, 5.684341886080802e-14, 5.684341886080802e-14, -4], [66, -5], [67, -30, -29, -28, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, [44, 45, 46, 47], -10, -9, -8, -7, -6]], [1, 375.00000000000006, 667, 0]], [11, "tishiNode", false, 1, [-55, -56, -57, -58, -59, -60, -61, -62], [[0, -53, [5, 750, 1334]], [10, 45, 1, 1, -54]]], [11, "statementView", false, 1, [-65, -66, -67, -68, -69, -70, -71], [[0, -63, [5, 750, 1334]], [10, 45, 1, 1, -64]]], [5, "lastServer", 1, [-75, -76, -77, -78, -79], [[0, -72, [5, 526, 45]], [14, 2, -73, 11], [57, false, 20, 426.171, -74]], [1, 0, -147.329, 0]], [11, "server", false, 1, [-82, -83, -84, -85, -86, -87], [[53, -80], [13, 18, 405, -81]]], [34, "svrItem", [-91, -92, -93, -94], [[0, -88, [5, 301, 51]], [22, -89, 24], [32, 3, -90, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]]]], [1, -2.5, -30.5, 0]], [11, "notice", false, 1, [-98, -99, -100, -101], [[0, -95, [5, 750, 1334]], [37, false, -96], [10, 45, 550, 620, -97]]], [5, "btnEnter", 1, [-107], [[0, -102, [5, 346, 88]], [8, 2, false, -103, 6], [7, 3, -105, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -104], [13, 4, 307.81600000000003, -106]], [1, 0, -315.18399999999997, 0]], [5, "btnLogin", 1, [-113], [[0, -108, [5, 346, 88]], [8, 2, false, -109, 7], [7, 3, -111, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -110], [13, 4, 308.81600000000003, -112]], [1, 0, -314.18399999999997, 0]], [34, "Toggle1", [-119, -120], [[0, -114, [5, 197, 44]], [38, 1, 0, false, -115, 18], [75, -118, [4, 4292269782], -117, -116]], [1, 0, -27, 0]], [35, "yiinsiNode", false, 1, [-123, -124, -125], [[0, -121, [5, 1, 1]], [13, 4, 276, -122]], [1, 0, -390.5, 0]], [28, "content", [10], [[3, -126, [5, 199, 49], [0, 0.5, 1]], [20, 41, 220, 0, -127], [77, 1, 2, 5, 7, true, -128], [83, -129, [[4, "37134kXueZKwLhkNPLRTV0E", "svrToggleEvents", 1]]]]], [11, "pop", false, 1, [-133, -134], [[0, -130, [5, 750, 1334]], [10, 45, 526, 259, -131], [74, -132]]], [19, "tips16", false, 1, [[0, -135, [5, 90, 115]], [14, 0, -136, 1], [58, 9, 30, 20, -137], [23, -138, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]]]], [1, -300, 589.5, 0]], [17, "btnNotice", 1, [[[0, -139, [5, 68, 69]], [18, false, -140, 2], -141, [59, 8, 26.226999999999975, -142]], 4, 4, 1, 4], [1, -314.773, 429.34, 0]], [5, "topRightNode", 1, [-146, -147], [[54, -143, [0, 0.5, 1]], [78, 2, 20, -144], [60, 33, 5, 35, -145]], [1, 320, 632, 0]], [29, "agree<PERSON><PERSON><PERSON>", 11, [-151], [[[0, -148, [5, 30, 30]], [14, 2, -149, 5], -150], 4, 4, 1], [1, -191.597, 0, 0]], [5, "Layout", 1, [-155, -156], [[3, -152, [5, 100, 120], [0, 0.5, 0]], [79, 1, 2, -153], [13, 4, 91, -154]], [1, 0, -576, 0]], [5, "Layout", 5, [-159, -160, -161], [[0, -157, [5, 360, 50]], [41, 1, 1, 60, -158]], [1, -5.684341886080802e-14, -363.935, 0]], [29, "list1", 5, [-166], [[[3, -162, [5, 199, 650], [0, 0.5, 1]], [15, 1, 0, -163, 19], [24, 0.23, 0.75, false, -164, 12], -165], 4, 4, 4, 1], [1, -161.412, 317, 0]], [12, "view", 20, [12], [[3, -167, [5, 199, 650], [0, 0.5, 1]], [25, -168], [26, -169, [4, 16777215]], [21, 45, 240, 250, 0, -170]]], [29, "list2", 5, [-176], [[[3, -171, [5, 310, 650], [0, 0.5, 1]], [15, 1, 0, -172, 25], [24, 0.23, 0.75, false, -174, -173], -175], 4, 4, 4, 1], [1, 97.909, 317, 0]], [12, "view", 22, [-181], [[3, -177, [5, 310, 650], [0, 0.5, 1]], [25, -178], [26, -179, [4, 16777215]], [21, 45, 240, 250, 0, -180]]], [12, "content", 23, [6], [[3, -182, [5, 310, 56], [0, 0.5, 1]], [20, 41, 220, 0, -183], [80, 1, 3, 2, 5, 8, 8, true, -184]]], [28, "view", [-189], [[3, -185, [5, 490, 550], [0, 0.5, 1]], [25, -186], [26, -187, [4, 16777215]], [21, 45, 240, 250, 0, -188]]], [12, "content", 25, [-193], [[3, -190, [5, 490, 37.8], [0, 0.5, 1]], [20, 41, 220, 0, -191], [81, 1, 2, true, -192]]], [12, "bg", 13, [-197, -198], [[0, -194, [5, 447, 259]], [38, 1, 0, false, -195, 31], [9, -196]]], [5, "Layout", 27, [-202, -203], [[0, -199, [5, 342, 1]], [41, 1, 1, 80, -200], [61, 4, 65.56899999999999, 1, -201]], [1, 0, -63.43100000000001, 0]], [5, "btnCancel", 28, [-208], [[0, -204, [5, 131, 59]], [8, 2, false, -205, 29], [7, 3, -207, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -206]], [1, -105.5, 0.46999999999997044, 0]], [5, "btnOk", 28, [-213], [[0, -209, [5, 131, 59]], [15, 1, 0, -210, 30], [7, 3, -212, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -211]], [1, 105.5, 0.46999999999997044, 0]], [5, "btnNo", 2, [-218], [[0, -214, [5, 346, 88]], [8, 2, false, -215, 34], [7, 3, -217, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -216]], [1, 0.6239999999999668, -104.38599999999997, 0]], [5, "btnYes", 2, [-223], [[0, -219, [5, 346, 88]], [8, 2, false, -220, 35], [7, 3, -222, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -221]], [1, -1.063000000000045, -212.36200000000002, 0]], [28, "view", [-228], [[3, -224, [5, 600, 770], [0, 0.5, 1]], [25, -225], [26, -226, [4, 16777215]], [21, 45, 240, 250, 0, -227]]], [12, "content", 33, [-232], [[3, -229, [5, 600, 37.8], [0, 0.5, 1]], [20, 41, 220, 0, -230], [82, 1, 2, true, true, -231]]], [11, "loadingView", false, 1, [-236], [[0, -233, [5, 750, 1334]], [10, 45, 100, 100, -234], [9, -235]]], [36, "gameVer", false, 1, [[[3, -237, [5, 4, 26.68], [0, 1, 0.5]], -238, [2, -239], [62, 36, 25, 33.60899999999999, 30, -240]], 4, 1, 4, 4], [1, 350, -623.66, 0]], [19, "btnSwich", false, 16, [[0, -241, [5, 2, 2]], [37, false, -242], [7, 3, -244, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -243]], [1, 0, -1, 0]], [19, "btnFix", false, 16, [[0, -245, [5, 68, 69]], [18, false, -246, 3], [7, 3, -248, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -247]], [1, 0, -34.5, 0]], [17, "statement", 11, [[[3, -249, [5, 383.03125, 25.2], [0, 0, 0.5]], [33, 20, "<outline width=2 color=black><color=#fff5e4>我已详细阅读并同意<color=#00ff00><on click=\"_onRichClick\" param=\"0\"><u>用户协议</u></on></color>和<color=#00ff00><on click=\"_onRichClick\" param=\"1\"><u>隐私政策</u></on></color></color></outline>", 1, 20, "黑体", -250], -251, [2, -252]], 4, 4, 1, 4], [1, -174.652, 0, 0]], [1, "svrBtnClose", 5, [[0, -253, [5, 58, 57]], [8, 2, false, -254, 14], [7, 3, -256, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -255]], [1, 265.764, 400.484, 0]], [6, "Sprite", 7, [[0, -257, [5, 750, 1334]], [31, 0, false, -258, [4, 3019898880], 26], [10, 45, 2, 2, -259], [9, -260]]], [5, "bg", 7, [-264], [[0, -261, [5, 571, 729]], [8, 2, false, -262, 27], [9, -263]], [1, 1.239000000000011, 0.18599999999999284, 0]], [5, "list2", 7, [25], [[3, -265, [5, 490, 550], [0, 0.5, 1]], [68, false, 1, 0, -266], [24, 0.23, 0.75, false, -267, 26]], [1, 3, 270.601, 0]], [17, "noticeRich", 26, [[[0, -268, [5, 490, 37.8]], -269, [42, -270], [2, -271]], 4, 1, 4, 4], [1, 0, -18.9, 0]], [1, "OutlineLabel", 27, [[0, -272, [5, 59.859375, 34.24]], [16, "提 示", 22, 22, "黑体", 24, false, true, 1, true, -273], [2, -274], [63, 1, 10.310999999999977, 1, -275]], [1, 0, 102.06900000000002, 0]], [6, "Sprite", 2, [[0, -276, [5, 750, 1334]], [31, 0, false, -277, [4, 3019898880], 32], [10, 45, 2, 2, -278], [9, -279]]], [6, "Sprite", 3, [[0, -280, [5, 750, 1334]], [31, 0, false, -281, [4, 3019898880], 36], [10, 45, 2, 2, -282], [9, -283]]], [1, "statementBtnClose", 3, [[0, -284, [5, 58, 57]], [18, false, -285, 39], [7, 3, -287, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], -286]], [1, 300.8229999999999, 423.145, 0]], [11, "errorTipsNode", false, 1, [-290], [[0, -288, [5, 400, 46]], [8, 0, false, -289, 43]]], [17, "logo", 1, [[[3, -291, [5, 750, 482], [0, 0.5, 1]], -292, [64, 1, -293]], 4, 1, 4], [1, 0, 667, 0]], [1, "Sprite", 1, [[0, -294, [5, 892, 64]], [8, 0, false, -295, 0], [13, 4, -5, -296]], [1, -5.684341886080802e-14, -640, 0]], [6, "Label", 8, [[0, -297, [5, 101.875, 34.24]], [27, "进入游戏", 24, 24, "黑体", 24, false, true, 1, true, -298, [4, 4288413694]], [2, -299]]], [6, "Label", 9, [[0, -300, [5, 101.875, 34.24]], [27, "登录游戏", 24, 24, "黑体", 24, false, true, 1, true, -301, [4, 4288413694]], [2, -302]]], [1, "advice", 1, [[0, -303, [5, 459, 91.28]], [86, "健康游戏忠告\n抵制不良游戏 拒绝盗版游戏 注意自我保护 谨防受骗上当\n适度游戏益脑 沉迷游戏伤身 合理安排时间 享受健康生活", 18, 18, "黑体", 28, false, 1, 1, -304], [65, 20, -5.684341886080802e-14, -305]], [1, -5.684341886080802e-14, -621.36, 0]], [36, "btnLabel", false, 18, [[[0, -306, [5, 300.7890625, 32.980000000000004]], -307, [23, -308, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]]]], 4, 1, 4], [1, 0, 16.490000000000002, 0]], [1, "tips", 4, [[0, -309, [5, 103.875, 37.5]], [43, "【换服】", 24, 24, "黑体", 25, false, true, 1, true, 3, -310], [2, -311]], [1, 105.69799999999992, 1.6399999999999864, 0]], [1, "name", 4, [[3, -312, [5, 185, 37.5], [0, 0, 0.5]], [87, "1服", 24, 24, "黑体", 25, 2, false, true, true, 3, -313, [4, 4284279425]], [2, -314]], [1, -125.36500000000007, 0.6399999999999864, 0]], [6, "bg", 5, [[0, -315, [5, 569, 799]], [15, 1, 0, -316, 12], [9, -317]]], [5, "zonesvr14", 5, [-320], [[0, -318, [5, 535, 46]], [14, 0, -319, 13]], [1, -3, 357.066, 0]], [6, "Label", 59, [[0, -321, [5, 126.34375, 34.24]], [16, "选择服务器", 24, 24, "黑体", 24, false, true, 1, true, -322], [2, -323]]], [5, "item", 19, [-325, -326], [[0, -324, [5, 80, 20]]], [1, -140, 0, 0]], [1, "name", 61, [[3, -327, [5, 40.703125, 26.68], [0, 0, 0.5]], [16, "维护", 18, 18, "黑体", 18, false, true, 1, true, -328], [2, -329]], [1, -9.757999999999981, 0.6399999999999864, 0]], [12, "item-001", 19, [-331, -332], [[0, -330, [5, 80, 20]]]], [1, "name", 63, [[3, -333, [5, 40.703125, 26.68], [0, 0, 0.5]], [27, "推荐", 18, 18, "黑体", 18, false, true, 1, true, -334, [4, 4279627566]], [2, -335]], [1, -9.757999999999981, 0.6399999999999864, 0]], [5, "item-002", 19, [-337, -338], [[0, -336, [5, 80, 20]]], [1, 140, 0, 0]], [1, "name", 65, [[3, -339, [5, 40.703125, 26.68], [0, 0, 0.5]], [27, "爆满", 18, 18, "黑体", 18, false, true, 1, true, -340, [4, 4285881087]], [2, -341]], [1, -9.757999999999981, 0.6399999999999864, 0]], [6, "label", 10, [[0, -342, [5, 189.15625, 34.24]], [88, "燃烧远征991-999", 24, 24, "黑体", 24, 2, false, true, true, -343], [2, -344]]], [35, "sigin", false, 6, [-347], [[0, -345, [5, 67, 57]], [22, -346, 23]], [1, 118.081, 2, 0]], [49, "label", 68, [[0, -348, [5, 69.25, 29.2]], [16, "上次登录", 16, 16, "黑体", 20, false, true, 1, true, -349], [2, -350]], [1, 5.813, 7.382, 0], [3, 0, 0, -0.3077801029606412, 0.951457517822807], [1, 0, 0, -35.851]], [1, "name", 6, [[3, -351, [5, 4, 34.24], [0, 0, 0.5]], [44, "", 24, 24, "黑体", 24, false, true, true, -352], [2, -353]], [1, -101, 0, 0]], [1, "title", 42, [[0, -354, [5, 54.9375, 37.5]], [43, "公告", 24, 24, "黑体", 25, false, true, 1, true, 3, -355], [2, -356]], [1, 2.995999999999924, 325.746, 0]], [1, "noticeBtnClose", 7, [[0, -357, [5, 58, 57]], [18, false, -358, 28], [23, -359, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]]]], [1, 276.94599999999997, 320.995, 0]], [6, "txt", 29, [[0, -360, [5, 54.78125, 29.2]], [16, "取 消", 20, 20, "黑体", 20, false, true, 1, true, -361], [2, -362]]], [6, "txt", 30, [[0, -363, [5, 54.78125, 29.2]], [16, "确 定", 20, 20, "黑体", 20, false, true, 1, true, -364], [2, -365]]], [1, "text", 13, [[0, -366, [5, 400, 35.5]], [89, "", 0, 18, 18, "黑体", 25, 3, true, true, -367], [2, -368]], [1, -3, 22.445, 0]], [6, "bg", 2, [[0, -369, [5, 650, 600]], [15, 1, 0, -370, 33], [9, -371]]], [1, "title", 2, [[0, -372, [5, 148.734375, 50.1]], [90, "温馨提示", 35, 35, "黑体", 35, false, true, true, 3, -373], [2, -374]], [1, -5.684341886080802e-14, 252.14599999999996, 0]], [1, "title1", 2, [[0, -375, [5, 515.765625, 136.39999999999998]], [91, "欢迎使用本游戏，在您使用本游戏前，请按照\n指引点击进入下方的相关协议并仔细\n阅读;如不同意，则您将无法进入游戏。", 25, 25, "黑体", false, true, true, 3, -376], [2, -377]], [1, -3.893000000000029, 128.29600000000005, 0]], [1, "user<PERSON>ey<PERSON>", 2, [[3, -378, [5, 157.515625, 37.8], [0, 0, 0.5]], [33, 30, "<outline width=2 color=black><color=#fff5e4><color=#00ff00><u>[用户协议]</u></on></color><color=#00ff00></outline>", 1, 30, "黑体", -379], [32, 3, -380, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]]]], [1, -192.99900000000005, 1.3630000000000564, 0]], [1, "y<PERSON><PERSON><PERSON><PERSON>", 2, [[3, -381, [5, 157.515625, 37.8], [0, 0, 0.5]], [33, 30, "<outline width=2 color=black><color=#fff5e4><color=#00ff00><u>[隐私协议]</u></on></color><color=#00ff00></outline>", 1, 30, "黑体", -382], [32, 3, -383, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]]]], [1, 32.89999999999992, 1.3630000000000564, 0]], [6, "Label", 31, [[0, -384, [5, 113.05078125, 50.1]], [45, "不同意", 35, 35, "黑体", 35, false, true, 1, true, 3, -385, [4, 4288413694]], [2, -386]]], [6, "Label", 32, [[0, -387, [5, 77.3671875, 50.1]], [45, "同意", 35, 35, "黑体", 35, false, true, 1, true, 3, -388, [4, 4288413694]], [2, -389]]], [6, "bg", 3, [[0, -390, [5, 650, 850]], [15, 1, 0, -391, 37], [9, -392]]], [1, "title", 3, [[0, -393, [5, 146.734375, 48.1]], [44, "用户协议", 35, 35, "黑体", 35, false, true, true, -394], [2, -395]], [1, -5.684341886080802e-14, 394.981, 0]], [5, "list", 3, [33], [[3, -396, [5, 600, 770], [0, 0.5, 1]], [24, 0.23, 0.75, false, -397, 34]], [1, -1.4860000000000468, 366.79099999999994, 0]], [6, "Sprite", 35, [[0, -398, [5, 82, 82]], [18, false, -399, 40], [97, true, -400, [41], 42]]], [6, "label", 49, [[0, -401, [5, 318.015625, 31.72]], [92, "默认文本默认文本默认文本默认", 22, 22, "黑体", 22, false, true, true, -402, [4, 4278255360]], [2, -403]]], [17, "lab2", 1, [[[0, -404, [5, 4, 26.68]], -405, [13, 4, 398.08000000000004, -406]], 4, 1, 4], [1, -12.595000000000084, -255.57999999999998, 0]], [30, "bg", 1, [[[0, -407, [5, 750, 1334]], -408], 4, 1]], [6, "Sprite", 11, [[0, -409, [5, 526, 45]], [69, false, -410, [4, 3372220415], 4]]], [51, "Checkmark", false, 17, [[[0, -411, [5, 36, 29]], -412], 4, 1]], [30, "txtBanHao", 18, [[[3, -413, [5, 700, 120], [0, 0.5, 0]], -414], 4, 1]], [1, "imgStatus", 4, [[0, -415, [5, 18, 18]], [70, 0, -416, 8, 9]], [1, -139.40400000000005, 1.1650000000000205, 0]], [1, "zonesvr2", 4, [[0, -417, [5, 35, 36]], [22, -418, 10]], [1, 157.8569999999999, 0, 0]], [1, "lastServer", 4, [[0, -419, [5, 320, 40]], [23, -420, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]]]], [1, -3, 0, 0]], [1, "imgStatus", 61, [[0, -421, [5, 18, 18]], [14, 0, -422, 15]], [1, -25.367999999999995, 1.1650000000000205, 0]], [1, "imgStatus", 63, [[0, -423, [5, 18, 18]], [14, 0, -424, 16]], [1, -25.367999999999995, 1.1650000000000205, 0]], [1, "imgStatus", 65, [[0, -425, [5, 18, 18]], [14, 0, -426, 17]], [1, -25.367999999999995, 1.1650000000000205, 0]], [30, "Checkmark", 10, [[[0, -427, [5, 197, 44]], -428], 4, 1]], [50, "selectBg", false, 6, [[0, -429, [5, 301, 51]], [22, -430, 20]]], [1, "imgStatus", 6, [[0, -431, [5, 21, 21]], [71, -432, 21, 22]], [1, -115.132, 0, 0]], [19, "whiteBg", false, 3, [[0, -433, [5, 420, 500]], [8, 0, false, -434, 38]], [1, -1.4865, -14.7745, 0]], [1, "WebView", 3, [[0, -435, [5, 600, 780]], [98, "", -436]], [1, -3.4865000000000075, -14.774499999999989, 0]], [1, "rich", 34, [[0, -437, [5, 590, 37.8]], [84, 30, "", 25, 590, "黑体", 1, -438]], [1, 0, -18.9, 0]], [99, "zoneServer", true, [1], [100, [101, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [102, [4, 4283190348], [0, 512, 512]], [103], [104], [105], [106], [107], [108]]], [52, "Camera", 1, [-439], [1, 0, 0, 1000]], [109, 0, 1073741824, 667, 0, 2000, 1073741824, 106, [4, 4278190080]], [39, 2, false, 89], [39, 2, false, 50], [7, 3, 15, [[4, "37134kXueZKwLhkNPLRTV0E", "onClick", 1]], [4, 4292269782], 15], [93, "", 18, 18, "黑体", 18, false, true, 36, [4, 4293195263]], [72, 91], [76, false, 17, [4, 4292269782], 17, [[4, "37134kXueZKwLhkNPLRTV0E", "agreeToggleEvent", 1]], 112], [42, 39], [94, "", 19, 18, "黑体", 24, 2, true, 1, true, 92], [95, "ICP备案号：京ICP备19037383号-15A", 18, 18, "黑体", 23, true, 1, true, 55], [73, 1, 0, false, 99], [46, 20, 10, [40]], [46, 22, 6, [40]], [85, 30, "", 1, 22, 490, "黑体", 44], [96, "", 18, 18, "黑体", 18, false, true, 88]], 0, [0, 0, 1, 0, 6, 107, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 7, 2, 0, 8, 121, 0, 9, 9, 0, 10, 8, 0, 11, 116, 0, 12, 109, 0, 13, 110, 0, 14, 108, 0, 15, 11, 0, 16, 49, 0, 17, 14, 0, 18, 35, 0, 19, 120, 0, 20, 119, 0, 21, 118, 0, 22, 115, 0, 23, 113, 0, 24, 114, 0, 25, 111, 0, 26, 3, 0, 27, 4, 0, 28, 13, 0, 29, 7, 0, 30, 5, 0, 0, 1, 0, -1, 106, 0, -2, 89, 0, -3, 50, 0, -4, 51, 0, -5, 14, 0, -6, 15, 0, -7, 36, 0, -8, 16, 0, -9, 11, 0, -10, 8, 0, -11, 9, 0, -12, 54, 0, -13, 18, 0, -14, 4, 0, -15, 5, 0, -16, 7, 0, -17, 13, 0, -18, 2, 0, -19, 3, 0, -20, 35, 0, -21, 49, 0, -22, 88, 0, 0, 2, 0, 0, 2, 0, -1, 46, 0, -2, 76, 0, -3, 77, 0, -4, 78, 0, -5, 79, 0, -6, 80, 0, -7, 31, 0, -8, 32, 0, 0, 3, 0, 0, 3, 0, -1, 47, 0, -2, 83, 0, -3, 84, 0, -4, 102, 0, -5, 103, 0, -6, 48, 0, -7, 85, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 93, 0, -2, 94, 0, -3, 56, 0, -4, 57, 0, -5, 95, 0, 0, 5, 0, 0, 5, 0, -1, 58, 0, -2, 59, 0, -3, 40, 0, -4, 19, 0, -5, 20, 0, -6, 22, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 100, 0, -2, 101, 0, -3, 68, 0, -4, 70, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 41, 0, -2, 42, 0, -3, 72, 0, -4, 43, 0, 0, 8, 0, 0, 8, 0, 2, 8, 0, 0, 8, 0, 0, 8, 0, -1, 52, 0, 0, 9, 0, 0, 9, 0, 2, 9, 0, 0, 9, 0, 0, 9, 0, -1, 53, 0, 0, 10, 0, 0, 10, 0, 31, 117, 0, 2, 10, 0, 0, 10, 0, -1, 99, 0, -2, 67, 0, 0, 11, 0, 0, 11, 0, -1, 90, 0, -2, 17, 0, -3, 39, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 27, 0, -2, 75, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -3, 110, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 37, 0, -2, 38, 0, 0, 17, 0, 0, 17, 0, -3, 113, 0, -1, 91, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 92, 0, -2, 55, 0, 0, 19, 0, 0, 19, 0, -1, 61, 0, -2, 63, 0, -3, 65, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, -4, 118, 0, -1, 21, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 32, 24, 0, 0, 22, 0, -4, 119, 0, -1, 23, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, -1, 24, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, -1, 26, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, -1, 44, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, -1, 45, 0, -2, 28, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, -1, 29, 0, -2, 30, 0, 0, 29, 0, 0, 29, 0, 2, 29, 0, 0, 29, 0, -1, 73, 0, 0, 30, 0, 0, 30, 0, 2, 30, 0, 0, 30, 0, -1, 74, 0, 0, 31, 0, 0, 31, 0, 2, 31, 0, 0, 31, 0, -1, 81, 0, 0, 32, 0, 0, 32, 0, 2, 32, 0, 0, 32, 0, -1, 82, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, -1, 34, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, -1, 104, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, -1, 86, 0, 0, 36, 0, -2, 111, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 2, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 2, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, -3, 114, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 2, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 42, 0, -1, 71, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, -2, 120, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 2, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, -1, 87, 0, 0, 50, 0, -2, 109, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, -2, 116, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, -1, 60, 0, 0, 60, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, -1, 96, 0, -2, 62, 0, 0, 62, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, -1, 97, 0, -2, 64, 0, 0, 64, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, -1, 98, 0, -2, 66, 0, 0, 66, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, -1, 69, 0, 0, 69, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, -2, 121, 0, 0, 88, 0, 0, 89, 0, -2, 108, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, -2, 112, 0, 0, 92, 0, -2, 115, 0, 0, 93, 0, 0, 93, 0, 0, 94, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, 0, 97, 0, 0, 97, 0, 0, 98, 0, 0, 98, 0, 0, 99, 0, -2, 117, 0, 0, 100, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, 0, 104, 0, 0, 104, 0, -1, 107, 0, 33, 105, 1, 3, 105, 6, 3, 24, 10, 3, 12, 12, 3, 21, 25, 3, 43, 33, 3, 85, 439], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 108, 109, 112, 117], [1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, 34, 1, -1, -2, -3, -4, 1, 1, 1, 1], [12, 13, 14, 15, 3, 16, 0, 0, 4, 6, 17, 3, 1, 18, 5, 19, 4, 20, 21, 22, 23, 4, 6, 24, 25, 26, 2, 27, 5, 28, 29, 1, 2, 1, 0, 0, 2, 1, 2, 5, 30, 7, 7, 3, 31, 8, 32, 33, 34, 8, 35, 36]], [[{"name": "logo_ma<PERSON>ian", "rect": {"x": 103, "y": 87, "width": 544, "height": 312}, "offset": {"x": 0, "y": -2}, "originalSize": {"width": 750, "height": 482}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-272, -156, 0, 272, -156, 0, -272, 156, 0, 272, 156, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [103, 395, 647, 395, 103, 83, 647, 83], "nuv": [0.13733333333333334, 0.17219917012448133, 0.8626666666666667, 0.17219917012448133, 0.13733333333333334, 0.8195020746887967, 0.8626666666666667, 0.8195020746887967], "minPos": {"x": -272, "y": -156, "z": 0}, "maxPos": {"x": 272, "y": 156, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [4], [37]]]]