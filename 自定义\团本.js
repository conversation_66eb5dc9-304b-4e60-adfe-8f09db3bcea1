var _world = globalThis;
var map = _world.MapManager;
_world.wait = async function (delay) {
    return new Promise(resolve => setTimeout(() => {
        resolve(0)
    }
        , delay))
};

_world.fb = [

    { map: "T_f1015", x: 25, y: 13, nextTime: 0 }, // 玛克纳萨斯(中枢)
    { map: "T_f1014", x: 25, y: 13, nextTime: 0 }, // 玛克纳萨斯(缝合间)
    { map: "T_f1013", x: 25, y: 13, nextTime: 0 }, // 玛克纳萨斯(军事区)
    { map: "T_f1012", x: 25, y: 13, nextTime: 0 }, // 玛克纳萨斯(实验室)
    { map: "T_f1011", x: 25, y: 13, nextTime: 0 }, // 玛克纳萨斯(育虫室)

    { map: "T_f1009", x: 78, y: 16, nextTime: 0 }, // 艾拉柯尼斯上
    { map: "T_f1010", x: 25, y: 13, nextTime: 0 }, // 艾拉柯尼斯下

    { map: "T_f1004", x: 16, y: 139, nextTime: 0 }, // 黑龙要塞上
    { map: "T_f1005", x: 68, y: 71, nextTime: 0 }, // 黑龙要塞下
    { map: "T_f1006", x: 79, y: 44, nextTime: 0 }, // 诺克蒂丝的巢穴
    // { map: "T_f1001", x: 27, y: 21, nextTime: 0 }, // 火焰之心(一区)
    // { map: "T_f1002", x: 43, y: 19, nextTime: 0 }, // 火焰之心(二区)
    // { map: "T_f1003", x: 1, y: 1, nextTime: 0 }, // 火焰之心(三区)

    // { map: "T_syf1001", x: 27, y: 21, nextTime: 0 }, // 深渊火焰之心(一区)
    // { map: "T_syf1002", x: 43, y: 19, nextTime: 0 }, // 深渊火焰之心(二区)
    // { map: "T_syf1003", x: 68, y: 99, nextTime: 0 }, // 深渊火焰之心(三区)

    { map: "T_syf1004", x: 16, y: 139, nextTime: 0 }, //深渊 黑龙要塞上
    { map: "T_syf1005", x: 68, y: 71, nextTime: 0 }, //深渊 黑龙要塞下
    { map: "T_syf1006", x: 79, y: 44, nextTime: 0 }, //深渊 诺克蒂丝的巢穴
    { map: "T_syf1009", x: 78, y: 16, nextTime: 0 }, // 【深渊】艾拉柯尼斯上
    { map: "T_syf1010", x: 25, y: 13, nextTime: 0 }, // 【深渊】艾拉柯尼斯下

    { map: "T_syf1014", x: 25, y: 13, nextTime: 0 }, // 【深渊】玛克纳萨斯(缝合间)
    { map: "T_syf1013", x: 25, y: 13, nextTime: 0 }, // 【深渊】玛克纳萨斯(军事区)
    { map: "T_syf1012", x: 25, y: 13, nextTime: 0 }, // 【深渊】玛克纳萨斯(实验室)
    { map: "T_syf1011", x: 25, y: 13, nextTime: 0 }, // 【深渊】玛克纳萨斯(育虫室)

    // { map: "T_f1007", x: 17, y: 15, nextTime: 0 }, // 库尔赫拉杜上
    // { map: "T_f1008", x: 105, y: 80, nextTime: 0 }, // 库尔赫拉杜下
];

_world.arg = {
    run: true,
};
_world.each = async function () {
    while (_world.arg.run) {
        // 找到最早可进入的副本
        let readyDungeon = _world.fb
            .filter(d => Date.now() >= d.nextTime)
            .sort((a, b) => a.nextTime - b.nextTime)[0];
        if (readyDungeon) {
            // 进入副本
            map.fly2Pos(readyDungeon.map, readyDungeon.x, readyDungeon.y);
            await _world.wait(2000);
            Net.sendPbMsg(214, { str: readyDungeon.map });
            await _world.wait(2000);
            // 设置下次可进入时间（1分钟后）
            readyDungeon.nextTime = Date.now() + 60 * 1000;
            await _world.wait(2000);
        } else {
            // 等待最早的副本刷新
            let nextReady = Math.min(..._world.fb.map(d => d.nextTime));
            let waitTime = nextReady - Date.now();
            await _world.wait(Math.max(waitTime, 1000));
        }
        const items = []
        ItemManager.getInstance().itemsInfo[4].forEach(i => {
            if (!i) return
            const cfg = ItemManager.getCfgById(i.tempId)
            if (!cfg) return
            if (cfg.quality >= 4 && cfg.itemLv >=80) return
            if (cfg.suitID > 0 && cfg.itemLv >=104) return
            if (cfg.itemLv ==105 && [16,17,7].includes(cfg.mainType)) return
            i.uuid && items.push(i.uuid)
        })
        if (items.length > 50) {
            Net.sendPbMsg(59, { items: items })
            await _world.wait(1000);
        }
    }
}

// 装备类型 EquipMainType

// var t = {
//     pBag: 0, // 共享背包
//     storage: 1, 仓库
//     bodyEquip1: 2, 挑战装备
//     bodyEquip2: 3, 冒险装备
//     pteEquipBag: 4, 装备栏
//     foodBag: 5, 食物背包
//     bank: 6, 银行
//     tuoguan: 7, 托管背包
//     pteItemBag: 8, 个人道具
//     runePackage: 9,
// }




// 从托管里面拿经验出来
var item = ItemManager.getInstance().itemsInfo[7].filter(i => i && i.tempId == "10010" && i.count == 9999999).map(i => { return { id: i.uuid, pos: i.currIndexY } }).shift();
if (item) {
    a.Id = item.id
    a.currIndexX = 7
    a.currIndexY = item.pos
    a.tarIndexX = 0
    a.tarIndexY = 200 + item.pos
} else {
    console.debug("没有了")
}

// 从背包拿出来放到托管
var item = ItemManager.getInstance().itemsInfo[0].filter(i => i && i.tempId == "10010" && i.count == 9999999).map(i => { return { id: i.uuid, pos: i.currIndexY } }).shift();
if (item) {
    a.Id = item.id
    a.currIndexX = 0
    a.currIndexY = item.pos
    a.tarIndexX = 7
    a.tarIndexY = ItemManager.getInstance().itemsInfo[7].findIndex(i => !i)
} else {
    console.debug("没有了")
}

// 刀哥    战士 288120534964636160
// 幸运星  猎人 **************9104
// 夏天    法师 288638297382586880
// 夏梦    牧师 288207369925560832
// 盛夏    圣骑 352368282022447616
// 


// n = [ {intValue: 0, intValue2: -1, strValue: "288069735349750272"}, {intValue: 0, intValue2: -1, strValue: "288827714651032064"},{intValue: 1, intValue2: -1, strValue: "352368282022447616"}]