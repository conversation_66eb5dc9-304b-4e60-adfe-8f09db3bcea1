<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Game Only</title>
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            overflow: hidden; 
            background: #000;
        }
        #myCanvas { 
            display: block; 
            width: 100vw;
            height: 100vh;
        }
    </style>
</head>
<body>
    <canvas id="myCanvas"></canvas>
    
    <!-- 引用wx.js文件 -->
    <script src="wx.js"></script>
    
    <script>
        // 页面加载完成后连接父窗口
        window.onload = function() {
            // 连接到父窗口的通信桥梁
            if (window.parent && window.parent.GameBridge) {
                window.parent.GameBridge.setGameWindow(window);
                console.log('已连接到父窗口');
            }
            
            // 这里可以启动你的cocos游戏
            // 例如：window.launchGame && window.launchGame();
        };
        
        // 窗口大小改变时调整canvas
        window.onresize = function() {
            const canvas = document.getElementById('myCanvas');
            if (canvas) {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }
        };
    </script>
</body>
</html>
