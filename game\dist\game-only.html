<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Game Only</title>
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            overflow: hidden; 
            background: #000;
        }
        #myCanvas { 
            display: block; 
            width: 100vw;
            height: 100vh;
        }
    </style>
</head>
<body>
    <canvas id="myCanvas"></canvas>
    
    <script>
        // 模拟wx环境 - 简化版本
        window.wx = {
            env: {
                USER_DATA_PATH: "src/test/remote",
            },
            downloadCounter: 0,
            downloadMap: {},
            
            downloadFile: function (options) {
                const { url, success, fail } = options
                const unique = Date.now() + "" + (this.downloadCounter++)
                this.downloadMap[unique] = options;
                // 模拟下载
                setTimeout(() => {
                    success && success({ tempFilePath: url, statusCode: 200 });
                }, 1000);
            },
            
            showToast: function(options) {
                console.log('Toast:', options.title);
            }
        };
        
        // 模拟GameGlobal
        window.GameGlobal = window;
        
        // 游戏状态
        let gameState = {
            score: 0,
            level: 1,
            isPlaying: false,
            playerHealth: 100
        };
        
        // 游戏方法 - 供Vue调用
        window.useItem = function(itemType) {
            console.log('使用道具:', itemType);
            if (itemType === 'potion') {
                gameState.playerHealth = Math.min(100, gameState.playerHealth + 20);
                updateParentState();
            }
        };
        
        window.pauseGame = function() {
            gameState.isPlaying = !gameState.isPlaying;
            console.log('游戏暂停状态:', !gameState.isPlaying);
            updateParentState();
        };
        
        window.saveGame = function() {
            console.log('游戏已保存');
            localStorage.setItem('gameState', JSON.stringify(gameState));
        };
        
        // 更新父窗口状态
        function updateParentState() {
            if (window.parent && window.parent.GameBridge) {
                Object.assign(window.parent.GameBridge.state, gameState);
            }
        }
        
        // 简单的游戏循环
        function gameLoop() {
            if (gameState.isPlaying) {
                gameState.score += 1;
                if (gameState.score % 100 === 0) {
                    gameState.level++;
                }
                updateParentState();
            }
            requestAnimationFrame(gameLoop);
        }
        
        // 启动游戏
        window.launchGame = function() {
            console.log('游戏启动');
            gameState.isPlaying = true;
            updateParentState();
            gameLoop();
            
            // 简单的canvas绘制
            const canvas = document.getElementById('myCanvas');
            const ctx = canvas.getContext('2d');
            
            function draw() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                
                ctx.fillStyle = '#001122';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#00ff00';
                ctx.font = '24px Arial';
                ctx.fillText(`分数: ${gameState.score}`, 20, 40);
                ctx.fillText(`等级: ${gameState.level}`, 20, 70);
                ctx.fillText(`血量: ${gameState.playerHealth}`, 20, 100);
                ctx.fillText(`状态: ${gameState.isPlaying ? '游戏中' : '暂停'}`, 20, 130);
                
                if (gameState.isPlaying) {
                    requestAnimationFrame(draw);
                } else {
                    setTimeout(draw, 100);
                }
            }
            draw();
        };
        
        // 页面加载完成后连接父窗口
        window.onload = function() {
            // 连接到父窗口的通信桥梁
            if (window.parent && window.parent.GameBridge) {
                window.parent.GameBridge.setGameWindow(window);
                console.log('已连接到父窗口');
            }
            
            // 自动启动游戏
            setTimeout(() => {
                launchGame();
            }, 500);
        };
        
        // 窗口大小改变时调整canvas
        window.onresize = function() {
            const canvas = document.getElementById('myCanvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };
    </script>
</body>
</html>
