// 把个人背包道具移动到共享
ItemManager.getInstance().itemsInfo[8].forEach(i => {
    if (!i) return
    const next = ItemManager.getInstance().itemsInfo[0].findIndex(i => i == null);
    if (next == -1) return
    Net.sendMsg(57, {
        Id: i.uuid,
        currIndexX: 8,
        currIndexY: i.currIndexY,
        tarIndexX: 0,
        tarIndexY: next
    })
})

// 把共享黑铁残片移动到个人背包
var moveItem = ItemManager.getInstance().itemsInfo[0].find(i => i.tempId == "I0020105");
var next = ItemManager.getInstance().itemsInfo[8].length;
Net.sendMsg(57, {
    Id: moveItem.uuid,
    currIndexX: 0,
    currIndexY: moveItem.currIndexY,
    tarIndexX: 8,
    tarIndexY: next
})


// 20钻石 + 5票 + 20精造
Net.sendMsgCallBack(829, { str: "ZhiFuBaoPcJiangLi" })


// 778 CeBianLanJiangLi
// 778 ZhuoMianJiangLi
// 778 HuaWeiZhuoMianJiangLi

Net.sendMsg(778, { str: "CeBianLanJiangLi" })
Net.sendMsg(778, { str: "ZhuoMianJiangLi" })
Net.sendMsg(778, { str: "HuaWeiZhuoMianJiangLi" })


// 团本邀请
["288120534964636160", "288638297382586880", "288207369925560832", "352368282022447616"].forEach((str) => Net.sendMsg(718, { intValue: 0, strValue: "TBb7wbrWOB@T_f1005@288069735349750272" }))

// 副本内一键开启
Net.sendMsg(345, null)

// 英雄本正常挂机  
// 组好队伍，进入副本后执行一次
var _world = globalThis;
_world.wait = async function (delay) { return new Promise(resolve => setTimeout(() => { resolve(0) }, delay)) };
_world.each = async () => {
    while (true) {
        await _world.wait(2000);
        if (!fubenView || !fubenView.fubenInfo) continue
        var nextBoss = (fubenView.fubenInfo.bossInfo || []).find(b => b.death == 0)
        if (nextBoss) {
            if (!Main.playerData.isAutoFighting && !Main.playerData.isAutoMoving) {
                fubenView.fuBenAuoAi && fubenView.fuBenAuoAi.gotoNearBossPos()
            }
            continue
        }
        Net.sendMsg(345, null)
        while (true) {
            await _world.wait(1000);
            if (!fubenView.tuichuBtn.active) continue
            var v = globalThis.fubenView
            globalThis.fubenView = null
            Net.sendMsg(346, null)
            break
        }
        const items = []
        ItemManager.getInstance().itemsInfo[4].forEach(i => {
            if (!i) return
            const cfg = ItemManager.getCfgById(i.tempId)
            if (!cfg) return
            i.uuid && items.push(i.uuid)
        })
        if (items.length > 5) {
            Net.sendPbMsg(59, { items: items })
            await _world.wait(1000);
        }
    }
}

/*
C2S_638 疑似1v1快速挑战
C2S_968 疑似3v3快速挑战
C2S_269 摧毁装备？
onCheckApply 同意入队申请？
C2S_210 C2S_190 传送可以研究一下
C2S_192 这个是位置改变后 设置区域的

C2S_7 作用是啥？
C2S_58 ?
C2S_1291 ?
C2S_12 ?
C2S_523 -> 白嫖皮肤

*/

// 加大索敌范围
globalThis.attackRange = true
// 加大攻击范围
globalThis.attackFindRange = true

// 天大bug 衬衣重置
Net.sendMsgCallBack(564, null, handle)

// 移动
Object.defineProperties(globalThis, {
    "move": {
        get: function () {
            return this._move
        },
        set: function (v) {
            this._move = v;
            v && Main.playerData.netData.setValue(32, 0.3);
        }
    },
})

// 清缴副本
var _world = globalThis;
_world.wait = async function (delay) { return new Promise(resolve => setTimeout(() => { resolve(0) }, delay)) };
_world.each = async () => {
    while (true) {
        Net.sendMsg(325, { str: "f1009" })
        await _world.wait(300);
        const items = []
        ItemManager.getInstance().itemsInfo[4].forEach(i => {
            if (!i) return
            const cfg = ItemManager.getCfgById(i.tempId)
            if (!cfg) return
            if (cfg.quality >= 4) return
            if (i.itemLv > 70 && i.forgeAttr?.length) {
                if (i.forgeAttr.filter(f => f.quality == 5).length >= 3) return
            }
            i.uuid && items.push(i.uuid)
        })
        if (items.length > 50) {
            Net.sendPbMsg(59, { items: items })
            await _world.wait(200);
        }
    }
}

// 重连 Net.sendMsg(38, {str:Main.playerData.id})

// 幻境 Net.sendMsgCallBack(949,{intValue: 1, intValue2: 116, strValue: "fhj10003"}, handle)
// 强行去酒吧 Net.sendMsgCallBack(949,{intValue: 5, intValue2: 50, strValue: "S_f1018"}, handle)
// 强行杀年兽 Net.sendMsgCallBack(949,{intValue: 5, intValue2: 50, strValue: "fns001"}, handle)


var _world = globalThis;
_world.fb = [];
_world.wait = async function (delay) { return new Promise(resolve => setTimeout(() => { resolve(0) }, delay)) }
_world.send = false
_world.sendCnt = 0
_world.mover = false
_world.boss = false
_world.each = async () => {
    while (true) {
        await _world.wait(1000)
        if (Main.playerData.curMapId == "m1012" && !_world.send || _world.sendCnt > 10) {
            Net.sendMsg(949,{intValue: 5, intValue2: 50, strValue: "S_f1018"})
            _world.send = true
            _world.sendCnt = 0
            continue
        }

        if (!fubenView) continue
        if (Main.playerData.curMapId != "S_f1018") {
            _world.sendCnt+=1
            continue
        }
        _world.sendCnt = 0
        if (!Main.playerData.isAutoFighting && !Main.playerData.isAutoMoving && !_world.mover) {
            PlayerControl.getInstance().startAutoMove("S_f1018", 21, 18, 1, { cmd: "autofight" })
            _world.mover = true;
            await _world.wait(2000);
            continue
        }

        const boss = ThingManager.instance.mThingArr.find(m => m._netData.getString(5005) == "S_Bs2019005")
        if (!boss && !_world.boss) {
            continue
        }
        _world.boss = true
        if (!boss || !boss._netData || boss._netData.getValue(3018)) {
            Net.sendMsg(214, { str: "S_f1018" })
            _world.mover = false
            _world.send = false
            _world.boss = false
            await _world.wait(1000)
        }
    }
}