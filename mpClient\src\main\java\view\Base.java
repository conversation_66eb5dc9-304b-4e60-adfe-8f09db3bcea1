package view;

import com.teamdev.jxbrowser.browser.Browser;
import com.teamdev.jxbrowser.browser.callback.AlertCallback;
import com.teamdev.jxbrowser.browser.callback.InjectJsCallback;
import com.teamdev.jxbrowser.browser.callback.StartDownloadCallback;
import com.teamdev.jxbrowser.browser.callback.input.PressKeyCallback;
import com.teamdev.jxbrowser.browser.event.ConsoleMessageReceived;
import com.teamdev.jxbrowser.browser.event.FrameCreated;
import com.teamdev.jxbrowser.download.Download;
import com.teamdev.jxbrowser.download.DownloadTarget;
import com.teamdev.jxbrowser.download.event.*;
import com.teamdev.jxbrowser.download.internal.rpc.StartDownload;
import com.teamdev.jxbrowser.frame.Frame;
import com.teamdev.jxbrowser.js.JsObject;
import com.teamdev.jxbrowser.navigation.event.LoadFinished;
import com.teamdev.jxbrowser.navigation.event.NavigationFinished;
import com.teamdev.jxbrowser.navigation.event.NavigationStarted;
import com.teamdev.jxbrowser.profile.Profile;
import com.teamdev.jxbrowser.profile.Profiles;
import com.teamdev.jxbrowser.ui.Progress;
import com.teamdev.jxbrowser.view.javafx.BrowserView;
import com.teamdev.jxbrowser.view.javafx.callback.DefaultCallback;
import env.Global;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.scene.layout.BorderPane;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.stage.WindowEvent;
import mgr.BrowserManager;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

public class Base extends Application {
    public BorderPane root;
    public Stage stage;

    protected String id = "10240";

    public String title;
    // 窗口宽高
    public double width;
    public double height;
    // 透明度
    public double opacity;

    // 浏览器组件
    public BrowserView browserView;
    // 浏览器对象
    public Browser browser;
    // 初始化后第一次的frame
    private Frame mainFrame;
    // 调试模式下，启动时打开调试窗口
    public boolean debug = false;
    // 缩放等级
    public double zoom;
    // 是否可以改变窗口尺寸
    public boolean resizeable = false;

    public String proName = "none_";

    // 如果开了调试窗口，这个就应该有值
    public Debug debugWindow;
    // 浏览器要加载的连接
    public String url;
    // 浏览器window对象
    public JsObject window;

    protected Scene scene;
    // stage show时的位置
    protected double initX = -1024;
    protected double initY = -1024;

    public String getId() {
        return id;
    }

    public void onInit() {
        if (this instanceof Game || this instanceof Debug) {
            // 加载窗口位置
            double[] pos = GAME_DATA_KEY.splitData(this.getPositionOnStart());
            if (pos != null) {
                this.initX = pos[0];
                this.initY = pos[1];
            }
            // 加载窗口大小
            double[] size = GAME_DATA_KEY.splitData(this.getSizeOnStart());
            if (size != null) {
                this.width = size[0];
                this.height = size[1];
            }
        }
        // dashboard 单独处理，读配置表
    }

    @Override
    public void start(Stage primaryStage) throws Exception {
        this.onInit();
        Platform.setImplicitExit(false);
        stage = primaryStage;

        root = new BorderPane();
        stage.setResizable(resizeable);
        scene = new Scene(root, this.width, this.height);
        root.setOpacity(this.opacity);
        // 设置图标
        Image image = new Image("/shijie.jpg");
        stage.getIcons().add(image);
        stage.setTitle(this.title);
        stage.setScene(scene);
        stage.setOnCloseRequest(event -> {
//            System.out.println("当前窗口: " + this.getClass().getName());
            this.onClose(event);
        });

        if (this.initX != -1024 || this.initY != -1024) {
            stage.setX(this.initX);
            stage.setY(this.initY);
        }
        stage.show();
        // 加载浏览器
        loadBrowser(root);
    }

    protected void loadBrowser(BorderPane root) {
        new Thread(() -> {
            try {
                System.out.println("@@@@@ createBrowser start " + this.getClass().getName());
                this.createBrowser();

                System.out.println("@@@@@ createBrowser end " + this.getClass().getName());
                System.out.println("@@@@@ createBrowserView start " + this.getClass().getName());
                this.createBrowserView();
                System.out.println("@@@@@ createBrowserView end " + this.getClass().getName());
                this.initProtocol();
                //Platform.runLater(() -> root.setCenter(browserView));
                Platform.runLater(() -> {
                    if (!Global.loading.hasLast()) {
                        Global.loading.setLast(browserView);
                    } else {
                        root.setCenter(browserView);
                    }
                });

                browser.on(ConsoleMessageReceived.class, evt -> {
                });

                browser.on(FrameCreated.class, evt -> {
                });

                // 在加载的网页上执行 JavaScript 之前
                browser.set(InjectJsCallback.class, params -> {
                    return InjectJsCallback.Response.proceed();
                });

                browser.navigation().on(LoadFinished.class, this::onLoadFinished);
                browser.navigation().on(NavigationFinished.class, this::onNavigationFinished);
                System.out.println("@@@@@ browser loadUrl start " + this.getClass().getName());
                this.browser.navigation().loadUrl(this.url);
                System.out.println("@@@@@ browser loadUrl end " + this.getClass().getName());
                // 绑定快捷键
                this.__bind_key_event();
                // hook处理alert事件
                this.browser.set(AlertCallback.class, (params, tell) -> {
                    if (params.message().contains("服务器连接失败")) {
                        this.execQuickNr("window.$message.error('服务器连接失败!',{duration: 60000})");
                    }
                    if (params.message().contains("平台登录出错")) {
                        this.execQuickNr("window.$message.error('平台登录出错,请重新登录!',{duration: 60000})");
                    }
                    // 其他消息暂不处理
                    tell.ok();
                });
                if (this instanceof Dashboard) {
                    this.browser.set(StartDownloadCallback.class, new MineStartDownloadCallback(this.browserView));
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    // 协议特殊处理被转义的字符
    private void initProtocol() {
        browser.navigation().on(NavigationStarted.class, event -> {
            try {
                String newUrl = URLDecoder.decode(event.url(), "UTF-8");
                if (!Objects.equals(newUrl, event.url())) {
                    event.navigation().stop();
                    event.navigation().loadUrl(newUrl);
                    System.out.println("Navigation reload : " + newUrl);
                }
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        });
    }

    protected void clearDebug() {
        this.debugWindow = null;
    }

    /**
     * 打开调试窗口 f12
     */
    protected void openDebugWindow() {
        Platform.runLater(() -> {
//            if (this.debug && Global.dev) {
            if (this.debug && Global.debug) {
                try {
                    if (this.debugWindow == null) {
                        String url = this.browser.devTools().remoteDebuggingUrl().get();
                        this.debugWindow = new Debug(url, this);
                        this.debugWindow.start(new Stage());
                        return;
                    }
                    this.debugWindow.stage.toFront();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 重新加载页面
     */
    protected void reloadPage() {
        System.out.println("no reloadPage logic");
    }

    protected void clearFrame() {
        this.mainFrame = null;
    }

    /**
     * 绑定快捷键 子类自己实现 onKeyEvent方法
     */
    public void __bind_key_event() {
        this.browser.set(PressKeyCallback.class, params -> {
            this.onKeyEvent(String.valueOf(params.event().keyCode()));
            return PressKeyCallback.Response.proceed();
        });
    }

    public void onKeyEvent(String key) {
        switch (key) {
            case "KEY_CODE_F5":
                this.reloadPage();
                break;
            case "KEY_CODE_F12":
                this.openDebugWindow();
                break;
            default:
                this.onUnknownKey(key);
                break;
        }
    }

    protected void onUnknownKey(String key) {
        this.execQuickNr("typeof ON_UNKNOWN_KEY != 'undefined' && ON_UNKNOWN_KEY('" + key + "')");
    }

    /**
     * 浏览器加载完成后 如有必要，子类自己实现
     * 这个事件视乎常常会被调用，尤其是窗口隐藏再打开后
     */
    public void onLoadFinished(LoadFinished loadFinished) {
        this.clearFrame();
        JsObject window = (JsObject) this.exec("window");
        if (window != null) {
            this.window = window;
            // 设置全局属性
            window.putProperty("VER_STR", Global.VER_STR);
            window.putProperty("VERSION", Global.VERSION);
            window.putProperty("DEBUG", Global.dev);
            window.putProperty("LOCAL_HTTP", Global.remote_http);
            window.putProperty("LOCAL_LOGIC", Global.local_logic);
            this.initEvt();
        }
    }


    /**
     * 导航加载完成后 如有必要，子类自己实现
     *
     * @param navigationFinished
     */
    public void onNavigationFinished(NavigationFinished navigationFinished) {

    }

    /**
     * 创建浏览器native 子类必须实现
     */
    public void createBrowser() {
        System.out.println("createBrowser 没被实现:" + this.getClass().getName());
    }

    /**
     * 注入浏览器事件
     */
    public void initEvt() {
        // (仅Game和Dashboard可用)
        if (this instanceof Game || this instanceof Dashboard) {

        }

    }

    public void onClose(WindowEvent event) {
        if (this.debugWindow != null) {
            this.debugWindow.onClose(event);
        }
        try {
            BrowserManager.getEngine().close();
            this.browser.close();
            this.stage.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.exit(0);
    }

    /**
     * 创建view组件
     */
    public void createBrowserView() {
        browserView = BrowserView.newInstance(browser);
        browserView.setPrefWidth(width - 6);
        browserView.setMinWidth(width - 6);
        browserView.setPrefHeight(height);
        browserView.setMinHeight(height);
        browserView.resize(width - 2, height);
//        browserView.dragAndDrop().disable();// 禁用拖放
    }

    /**
     * call 浏览器 执行代码
     *
     * @param jsCode js代码
     * @return
     */
    public Object exec(String jsCode) {
        try {
            return this.getFrame().executeJavaScript(jsCode);
        } catch (Exception e) {
            return "";
        }
    }


    public void execQuickNr(String jsCode) {
        Platform.runLater(() -> this.execNr(jsCode, null));
    }

    public void execNr(String jsCode, Consumer<?> consumer) {
        if (consumer == null) {
            consumer = any -> {

            };
        }
        Consumer<?> finalConsumer = consumer;
        Platform.runLater(() -> {
            this.getFrame().executeJavaScript(jsCode, finalConsumer);
        });
    }

    public void simpleNotify(String any) {
        this.execNr("sampleNotify(" + any + ")", null);
    }

    public Frame getFrame() {
        if (this.mainFrame == null) {
            Optional<Frame> frame = browser.mainFrame();
            frame.ifPresent(value -> this.mainFrame = value);
        }
        return this.mainFrame;
    }

    /**
     * 获取启动参数列表
     *
     * @return
     */
    public List<String> Args() {
        return getParameters().getRaw();
    }

    public Profile checkAndGetProfile() {
        boolean create = true;
        Profiles profiles = BrowserManager.getEngine().profiles();
        Profile pro = null;
        for (Profile p : profiles.list()) {
            if (p.name().equals(proName)) {
                create = false;
                pro = p;
                break;
            }
        }
        if (create) {
            pro = profiles.newProfile(proName);
        }
        return pro;
    }

    /**
     * 关闭时保存位置
     */
    public void setPositionOnClose() {
        if (this instanceof Game) {
            Global.dashboard.execQuickNr("localStorage.setItem('" + GAME_DATA_KEY.POSITION_GAME + this.getId() + "','" + this.stage.getX() + "," + this.stage.getY() + "');");
        }
        if (this instanceof Debug) {
            Global.dashboard.execQuickNr("localStorage.setItem('" + GAME_DATA_KEY.POSITION_DEBUG + this.getId() + "','" + this.stage.getX() + "," + this.stage.getY() + "');");
        }

    }

    /**
     * 关闭时保存窗口尺寸
     */
    public void setSceneSizeOnClose() {
        double width = this.scene.widthProperty().doubleValue();
        double height = this.scene.heightProperty().doubleValue();
        if (this instanceof Game) {
            if (width <= 0) {
                width = Game.defaultWidth;
            }
            if (height <= 0) {
                height = Game.defaultHeight;
            }
            Global.dashboard.execQuickNr("localStorage.setItem('" + GAME_DATA_KEY.SIZE_GAME + this.getId() + "','" + width + "," + height + "');");
        }
        if (this instanceof Debug) {
            Global.dashboard.execQuickNr("localStorage.setItem('" + GAME_DATA_KEY.SIZE_DEBUG + this.getId() + "','" + width + "," + height + "');");
        }

    }

    /**
     * 获取启动时的位置
     *
     * @return ""
     */
    public String getPositionOnStart() {
        if (this instanceof Game) {
            return (String) Global.dashboard.exec("localStorage.getItem('" + GAME_DATA_KEY.POSITION_GAME + this.getId() + "');");
        }

        if (this instanceof Debug) {
            return (String) Global.dashboard.exec("localStorage.getItem('" + GAME_DATA_KEY.POSITION_DEBUG + this.getId() + "');");
        }

        return "";
    }


    /**
     * 获取启动时的窗口尺寸
     *
     * @return ""
     */
    public String getSizeOnStart() {
        if (this instanceof Game) {
            return (String) Global.dashboard.exec("localStorage.getItem('" + GAME_DATA_KEY.SIZE_GAME + this.getId() + "');");
        }
        if (this instanceof Debug) {
            return (String) Global.dashboard.exec("localStorage.getItem('" + GAME_DATA_KEY.SIZE_DEBUG + this.getId() + "');");
        }
        return "";
    }

}

class GAME_DATA_KEY {
    public static String POSITION_GAME = "game_position_";
    public static String SIZE_GAME = "game_size_";
    public static String ZOOM_GAME = "game_zoom_";

    public static String POSITION_DEBUG = "debug_position_";
    public static String SIZE_DEBUG = "debug_size_";

    public static double[] splitData(String data) {
        if (data != null && !data.equals("")) {
            double[] result = new double[2];
            String[] split = data.split(",");
            result[0] = Double.parseDouble(split[0]);
            result[1] = Double.parseDouble(split[1]);
            return result;
        }
        return null;
    }

}

final class MineStartDownloadCallback extends DefaultCallback implements StartDownloadCallback {

    public MineStartDownloadCallback(BrowserView parent) {
        super(parent);
    }

    public void on(StartDownloadCallback.Params params, StartDownloadCallback.Action tell) {
        Platform.runLater(() -> {
            Download download = params.download();
            FileChooser fileChooser = new FileChooser();
            fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("(*.*)", "*.*"));
            fileChooser.setInitialFileName(download.target().suggestedFileName());
            this.window().ifPresent((window) -> {
                File file = fileChooser.showSaveDialog(((BrowserView) this.widget()).getScene().getWindow());
                if (file != null) {
                    //下载完成
                    download.on(DownloadFinished.class, event -> {
                        Global.dashboard.execQuickNr("typeof window.DownloadFinished != 'undefined' && window.DownloadFinished()");
                    });
                    // 下载进度更新
                    download.on(DownloadUpdated.class, event -> {
                        Progress progress = event.progress().get();
                        long currentSpeed = event.currentSpeed();
                        long totalBytes = event.totalBytes();
                        long receivedBytes = event.receivedBytes();
                        Global.dashboard.execQuickNr("typeof window.DownloadUpdated != 'undefined' && window.DownloadUpdated(" + progress.value() + "," + currentSpeed + ", " + receivedBytes + ", " + totalBytes + ")");
                    });
                    // 下载暂停
                    download.on(DownloadPaused.class, event -> {
                        Global.dashboard.execQuickNr("typeof window.DownloadPaused != 'undefined' && window.DownloadPaused()");
                    });
                    // 下载中断
                    download.on(DownloadInterrupted.class, event -> {
                        DownloadInterruptReason reason = event.reason();
                        Global.dashboard.execQuickNr("typeof window.DownloadInterrupted != 'undefined' && window.DownloadInterrupted('" + reason.name() + "')");
                    });

                    tell.download(file.toPath());
                } else {
                    tell.cancel();
                }
            });
        });
    }
}
