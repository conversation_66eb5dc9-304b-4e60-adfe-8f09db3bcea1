import {fileURLToPath, URL} from 'node:url'
import {readFileSync} from 'fs'
import {resolve} from 'path'

import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import {NaiveUiResolver} from "unplugin-vue-components/resolvers";
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {viteSingleFile} from "vite-plugin-singlefile";

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        vue(),
        viteSingleFile({
            // 删除内联的CSS和JS注释
            removeViteModuleLoader: true,
        }),
        // 自定义插件来处理public目录下的文件
        {
            name: 'inline-public-files',
            transformIndexHtml: {
                order: 'post',
                handler(html, ctx) {
                    try {
                        const wxJsPath = resolve(__dirname, 'public/wx.js');
                        const wxJsContent = readFileSync(wxJsPath, 'utf-8');

                        // 在</head>标签前插入wx.js内容
                        return html.replace(
                            '</head>',
                            `<script>\n${wxJsContent}\n</script>\n</head>`
                        );
                    } catch (error) {
                        console.warn('无法读取public/wx.js文件:', error);
                        return html;
                    }
                }
            }
        },
        AutoImport({
            // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
            imports: [
                'vue',
                {
                    'naive-ui': [
                        'useDialog',
                        'useMessage',
                        'useNotification',
                        'useLoadingBar',
                    ],
                },
            ],
            dts: './auto-imports.d.ts',
            eslintrc: {
                enabled: false, // 1、true时生成eslint配置文件，2、生成后改为false，避免重复消耗
            },
        }),
        Components({
            resolvers: [NaiveUiResolver()],
        }),
    ],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        }
    }
})
